import customtkinter as ctk
import json
import time
import os
import random
import threading
from functools import partial
import networkx as nx
import matplotlib
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import google.generativeai as genai
import textwrap
import requests

# ==============================================================================
# ---- 设计系统 (Design System) ----
# ==============================================================================
class DesignSystem:
    """Notion风格设计系统 - 优雅、简洁、现代的浅色主题"""

    # === 背景色系 - Notion的温暖白色调 ===
    PRIMARY_BG = "#ffffff"          # 纯白主背景
    SECONDARY_BG = "#f7f6f3"        # 温暖的浅灰背景（Notion经典色）
    TERTIARY_BG = "#f1f1ef"         # 更深的浅灰背景
    HOVER_BG = "#f5f5f4"            # 悬停背景
    SELECTED_BG = "#e9e9e7"         # 选中背景
    CARD_BG = "#ffffff"             # 卡片背景

    # === 文本色系 - 层次分明的灰色 ===
    TEXT_PRIMARY = "#37352f"        # 主要文本（深灰，不是纯黑）
    TEXT_SECONDARY = "#6f6e69"      # 次要文本
    TEXT_MUTED = "#9b9a97"          # 弱化文本
    TEXT_PLACEHOLDER = "#c7c7c5"    # 占位符文本

    # === 边框和分割线 ===
    BORDER_LIGHT = "#e9e9e7"        # 浅边框
    BORDER_MEDIUM = "#d3d1cb"       # 中等边框
    BORDER_STRONG = "#c7c7c5"       # 强边框

    # === Notion经典功能色 ===
    # 主要操作色 - Notion蓝
    ACCENT_BLUE = "#2383e2"         # Notion蓝
    ACCENT_BLUE_LIGHT = "#e7f3ff"   # 浅蓝背景
    ACCENT_BLUE_HOVER = "#1a73d1"   # 蓝色悬停

    # 成功和确认 - Notion绿
    ACCENT_GREEN = "#0f7b0f"        # Notion绿
    ACCENT_GREEN_LIGHT = "#e7f5e7"  # 浅绿背景
    ACCENT_GREEN_HOVER = "#0d6b0d"  # 绿色悬停

    # 警告和注意 - Notion橙
    ACCENT_ORANGE = "#d9730d"       # Notion橙
    ACCENT_ORANGE_LIGHT = "#fdf2e9" # 浅橙背景
    ACCENT_ORANGE_HOVER = "#c7650c" # 橙色悬停

    # 错误和删除 - Notion红
    ACCENT_RED = "#e03e3e"          # Notion红
    ACCENT_RED_LIGHT = "#ffeaea"    # 浅红背景
    ACCENT_RED_HOVER = "#d73535"    # 红色悬停

    # 特殊功能 - Notion紫
    ACCENT_PURPLE = "#9065b0"       # Notion紫
    ACCENT_PURPLE_LIGHT = "#f4f0f7" # 浅紫背景
    ACCENT_PURPLE_HOVER = "#825a9b" # 紫色悬停

    # 中性色 - Notion灰
    ACCENT_GRAY = "#787774"         # Notion灰
    ACCENT_GRAY_LIGHT = "#f1f1ef"   # 浅灰背景
    ACCENT_GRAY_HOVER = "#6b6966"   # 灰色悬停

    # === 语义色彩（兼容性） ===
    SUCCESS = ACCENT_GREEN
    WARNING = ACCENT_ORANGE
    ERROR = ACCENT_RED
    INFO = ACCENT_BLUE

    # === 字体系统 - Notion的字体大小 ===
    FONT_TITLE = 24                 # 页面标题
    FONT_HEADING = 20               # 区域标题
    FONT_SUBHEADING = 16            # 子标题
    FONT_LARGE = 16                 # 大字体（兼容性）
    FONT_MEDIUM = 14                # 中等字体
    FONT_NORMAL = 14                # 正文
    FONT_SMALL = 12                 # 小字
    FONT_TINY = 11                  # 极小字

    # === 间距系统 - Notion的8px网格 ===
    SPACING_XXS = 2                 # 极小间距
    SPACING_XS = 4                  # 很小间距
    SPACING_SM = 8                  # 小间距
    SPACING_MD = 12                 # 中等间距
    SPACING_LG = 16                 # 大间距
    SPACING_XL = 24                 # 很大间距
    SPACING_XXL = 32                # 极大间距
    SPACING_HUGE = 48               # 巨大间距

    # === 圆角系统 ===
    RADIUS_SM = 3                   # 小圆角
    RADIUS_MD = 6                   # 中等圆角
    RADIUS_LG = 8                   # 大圆角
    RADIUS_XL = 12                  # 很大圆角

# ==============================================================================
# ---- PATH SETUP & GLOBAL CONSTANTS ----
# ==============================================================================
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_FILE = os.path.join(SCRIPT_DIR, "config.json")
DB_FILE = os.path.join(SCRIPT_DIR, "ideas.json")

try:
    matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    matplotlib.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"Font setup failed, graph labels may not display correctly: {e}")

STATUS_LIST = ["🌱 种子", "💡 构思中", "🛠️ 开发中", "✅ 已完成", "📦 已归档"]
DEFAULT_STATUS = STATUS_LIST[0]
STATUS_COLORS = {
    "🌱 种子": "#6a994e", "💡 构思中": "#a98467", "🛠️ 开发中": "#fca311",
    "✅ 已完成": "#147df5", "📦 已归档": "#6c757d"
}

# 想法分类系统
CATEGORY_LIST = ["💡 原创想法", "📚 知识笔记", "🤖 AI生成", "🔗 链接收藏", "📝 草稿"]
DEFAULT_CATEGORY = CATEGORY_LIST[0]
CATEGORY_COLORS = {
    "💡 原创想法": DesignSystem.ACCENT_BLUE,
    "📚 知识笔记": DesignSystem.ACCENT_GREEN,
    "🤖 AI生成": DesignSystem.ACCENT_PURPLE,
    "🔗 链接收藏": DesignSystem.ACCENT_ORANGE,
    "📝 草稿": DesignSystem.ACCENT_GRAY
}
CATEGORY_ICONS = {
    "💡 原创想法": "💡",
    "📚 知识笔记": "📚",
    "🤖 AI生成": "🤖",
    "🔗 链接收藏": "🔗",
    "📝 草稿": "📝"
}

# ==============================================================================
# ---- HELPER WINDOW CLASSES ----
# ==============================================================================

class TutorialWindow(ctk.CTkToplevel):
    """新手教程窗口 - 引导用户了解应用的核心功能"""

    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.title("🎓 第二大脑 - 新手教程")
        self.geometry("800x600")
        self.transient(master)
        self.grab_set()

        # 教程步骤数据
        self.tutorial_steps = [
            {
                "title": "欢迎来到第二大脑！",
                "content": "这是一个智能想法管理工具，帮助您构建知识网络，实现创意涌现。\n\n🧠 核心理念：从连接到涌现\n💡 让想法之间产生化学反应\n🔗 发现隐藏的知识联系",
                "image": "🧠",
                "tips": ["每个想法都是知识网络中的一个节点", "AI会帮助您发现想法之间的联系", "通过连接产生新的创意灵感"]
            },
            {
                "title": "创建您的第一个想法",
                "content": "点击左侧的「✨ 新建想法」按钮开始创建。\n\n📝 核心想法：简洁描述您的想法\n💭 背景来源：想法的起源和背景\n🚀 潜在应用：可能的应用场景\n❓ 相关问题：延伸的思考问题",
                "image": "✨",
                "tips": ["从一个简单的想法开始", "不要担心不够完美，可以随时完善", "AI会帮助您扩展和优化想法"]
            },
            {
                "title": "智能标签系统",
                "content": "标签帮助您组织和分类想法。\n\n🏷️ 自动标签：AI根据内容自动生成\n🎯 手动标签：您可以添加自定义标签\n🔍 智能搜索：通过标签快速找到相关想法",
                "image": "🏷️",
                "tips": ["使用有意义的标签名称", "标签可以表示主题、领域或状态", "相同标签的想法会自动关联"]
            },
            {
                "title": "AI创作助手",
                "content": "右侧的AI助手是您的智能伙伴。\n\n🤖 智能推荐：基于当前想法推荐相关内容\n⚡ 关键词碰撞：组合不同概念产生新想法\n🔗 自动链接：发现想法之间的潜在联系",
                "image": "🤖",
                "tips": ["AI需要一些时间来学习您的思维模式", "多使用AI功能，效果会越来越好", "AI建议仅供参考，最终决定权在您"]
            },
            {
                "title": "知识图谱视图",
                "content": "图谱视图让您直观看到想法之间的连接。\n\n🌐 网络视图：想法以节点形式展示\n🔗 连接线：显示想法之间的关系\n🎯 聚焦模式：专注于特定想法的关联",
                "image": "🌐",
                "tips": ["定期查看图谱，发现新的连接机会", "密集连接的区域可能是重要主题", "孤立的想法需要更多关联"]
            },
            {
                "title": "高级功能探索",
                "content": "掌握这些高级功能，让您的第二大脑更强大。\n\n📤 导出功能：多格式导出您的想法\n🗑️ 回收站：安全删除和恢复想法\n⚙️ 个性化设置：自定义您的使用体验",
                "image": "⚙️",
                "tips": ["定期备份您的想法数据", "尝试不同的导出格式", "根据使用习惯调整设置"]
            }
        ]

        self.current_step = 0
        self.setup_ui()

    def setup_ui(self):
        """设置教程界面"""
        # 主容器
        main_frame = ctk.CTkFrame(self, fg_color=DesignSystem.PRIMARY_BG)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 顶部进度条
        self.progress_frame = ctk.CTkFrame(main_frame, fg_color=DesignSystem.SECONDARY_BG, height=60)
        self.progress_frame.pack(fill="x", padx=20, pady=(20, 0))
        self.progress_frame.pack_propagate(False)

        # 进度指示器
        progress_container = ctk.CTkFrame(self.progress_frame, fg_color="transparent")
        progress_container.pack(expand=True)

        self.progress_label = ctk.CTkLabel(
            progress_container,
            text=f"第 1 步，共 {len(self.tutorial_steps)} 步",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        )
        self.progress_label.pack(pady=10)

        self.progress_bar = ctk.CTkProgressBar(progress_container, width=300)
        self.progress_bar.pack(pady=(0, 10))
        self.progress_bar.set(1 / len(self.tutorial_steps))

        # 内容区域
        self.content_frame = ctk.CTkFrame(main_frame, fg_color=DesignSystem.SECONDARY_BG)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 底部按钮区
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent", height=60)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        button_frame.pack_propagate(False)

        # 按钮容器
        button_container = ctk.CTkFrame(button_frame, fg_color="transparent")
        button_container.pack(expand=True)

        self.prev_button = ctk.CTkButton(
            button_container,
            text="← 上一步",
            command=self.prev_step,
            state="disabled",
            width=100,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.ACCENT_BLUE,
            hover_color=DesignSystem.ACCENT_BLUE
        )
        self.prev_button.pack(side="left", padx=(0, 10), pady=15)

        self.next_button = ctk.CTkButton(
            button_container,
            text="下一步 →",
            command=self.next_step,
            width=100,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            fg_color=DesignSystem.ACCENT_GREEN,
            hover_color=DesignSystem.ACCENT_GREEN
        )
        self.next_button.pack(side="left", padx=10, pady=15)

        self.skip_button = ctk.CTkButton(
            button_container,
            text="跳过教程",
            command=self.skip_tutorial,
            width=100,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color="transparent",
            hover_color=DesignSystem.TERTIARY_BG,
            text_color=DesignSystem.TEXT_SECONDARY,
            border_width=1,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.skip_button.pack(side="right", padx=(10, 0), pady=15)

        # 显示第一步内容
        self.update_content()

    def update_content(self):
        """更新教程内容显示"""
        # 清空内容区域
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        step = self.tutorial_steps[self.current_step]

        # 内容滚动区域
        content_scroll = ctk.CTkScrollableFrame(self.content_frame, fg_color="transparent")
        content_scroll.pack(fill="both", expand=True, padx=30, pady=30)

        # 大图标
        icon_label = ctk.CTkLabel(
            content_scroll,
            text=step["image"],
            font=ctk.CTkFont(size=80),
        )
        icon_label.pack(pady=(0, 20))

        # 标题
        title_label = ctk.CTkLabel(
            content_scroll,
            text=step["title"],
            font=ctk.CTkFont(size=DesignSystem.FONT_LARGE, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        title_label.pack(pady=(0, 20))

        # 内容描述
        content_label = ctk.CTkLabel(
            content_scroll,
            text=step["content"],
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            justify="left",
            wraplength=600
        )
        content_label.pack(pady=(0, 30))

        # 小贴士区域
        if step.get("tips"):
            tips_frame = ctk.CTkFrame(content_scroll, fg_color=DesignSystem.TERTIARY_BG, corner_radius=DesignSystem.SPACING_SM)
            tips_frame.pack(fill="x", pady=(0, 20))

            tips_title = ctk.CTkLabel(
                tips_frame,
                text="💡 小贴士",
                font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
                text_color=DesignSystem.TEXT_PRIMARY
            )
            tips_title.pack(anchor="w", padx=20, pady=(15, 10))

            for tip in step["tips"]:
                tip_label = ctk.CTkLabel(
                    tips_frame,
                    text=f"• {tip}",
                    font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
                    text_color=DesignSystem.TEXT_SECONDARY,
                    justify="left",
                    anchor="w",
                    wraplength=550
                )
                tip_label.pack(anchor="w", padx=30, pady=2)

            # 底部间距
            ctk.CTkLabel(tips_frame, text="", height=10).pack()

        # 更新进度
        self.progress_label.configure(text=f"第 {self.current_step + 1} 步，共 {len(self.tutorial_steps)} 步")
        self.progress_bar.set((self.current_step + 1) / len(self.tutorial_steps))

        # 更新按钮状态
        self.prev_button.configure(state="normal" if self.current_step > 0 else "disabled")

        if self.current_step == len(self.tutorial_steps) - 1:
            self.next_button.configure(text="完成教程", fg_color=DesignSystem.ACCENT_GREEN)
        else:
            self.next_button.configure(text="下一步 →", fg_color=DesignSystem.ACCENT_GREEN)

    def next_step(self):
        """下一步"""
        if self.current_step < len(self.tutorial_steps) - 1:
            self.current_step += 1
            self.update_content()
        else:
            self.complete_tutorial()

    def prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self.update_content()

    def skip_tutorial(self):
        """跳过教程"""
        self.master.tutorial_completed = True
        self.destroy()

    def complete_tutorial(self):
        """完成教程"""
        self.master.tutorial_completed = True

        # 显示完成消息
        completion_window = ctk.CTkToplevel(self)
        completion_window.title("🎉 教程完成")
        completion_window.geometry("400x300")
        completion_window.transient(self)
        completion_window.grab_set()

        # 完成内容
        ctk.CTkLabel(
            completion_window,
            text="🎉",
            font=ctk.CTkFont(size=60)
        ).pack(pady=(40, 20))

        ctk.CTkLabel(
            completion_window,
            text="恭喜！教程完成",
            font=ctk.CTkFont(size=DesignSystem.FONT_LARGE, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        ).pack(pady=(0, 10))

        ctk.CTkLabel(
            completion_window,
            text="现在您可以开始使用第二大脑\n构建您的知识网络了！",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            justify="center"
        ).pack(pady=(0, 30))

        ctk.CTkButton(
            completion_window,
            text="开始使用",
            command=lambda: [completion_window.destroy(), self.destroy()],
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            fg_color=DesignSystem.ACCENT_GREEN,
            hover_color=DesignSystem.ACCENT_GREEN,
            width=120,
            height=40
        ).pack(pady=20)

class SettingsWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.title("网络与API设置")
        self.geometry("600x400")
        self.transient(master)
        self.grab_set()

        ctk.CTkLabel(self, text="Gemini API Key:", font=ctk.CTkFont(size=14, weight="bold")).pack(padx=20, pady=(20, 5), anchor="w")
        self.api_key_entry = ctk.CTkEntry(self, placeholder_text="在此输入你的 Gemini API Key", width=560)
        self.api_key_entry.pack(padx=20, pady=5, fill="x")
        self.api_key_entry.insert(0, self.master.api_key or "")

        ctk.CTkLabel(self, text="HTTP 代理地址 (可选):", font=ctk.CTkFont(size=14, weight="bold")).pack(padx=20, pady=(20, 5), anchor="w")
        self.proxy_entry = ctk.CTkEntry(self, placeholder_text="例如: http://127.0.0.1:7890", width=560)
        self.proxy_entry.pack(padx=20, pady=5, fill="x")
        self.proxy_entry.insert(0, self.master.proxy_url or "")

        self.test_button = ctk.CTkButton(self, text="测试连接", command=self.test_connection)
        self.test_button.pack(padx=20, pady=20)
        
        self.test_status_label = ctk.CTkLabel(self, text="")
        self.test_status_label.pack(padx=20, pady=5)

        self.save_button = ctk.CTkButton(self, text="保存并关闭", command=self.save_and_close, height=40)
        self.save_button.pack(padx=20, pady=20, side="bottom", fill="x")

    def save_and_close(self):
        new_api_key = self.api_key_entry.get().strip()
        new_proxy_url = self.proxy_entry.get().strip()
        
        config_data = { "GEMINI_API_KEY": new_api_key, "HTTP_PROXY": new_proxy_url }
        
        try:
            with open(CONFIG_FILE, "w") as f:
                json.dump(config_data, f, indent=4)
            self.master.api_key = new_api_key
            self.master.proxy_url = new_proxy_url
            self.destroy()
        except Exception as e:
            self.test_status_label.configure(text=f"❌ 保存失败: {e}", text_color="red")
    
    def test_connection(self):
        self.test_status_label.configure(text="测试中...", text_color="gray")
        self.test_button.configure(state="disabled")
        threading.Thread(target=self._test_worker, daemon=True).start()

    def _test_worker(self):
        api_key = self.api_key_entry.get().strip()
        proxy_url = self.proxy_entry.get().strip()

        # 如果没有API Key，提示用户
        if not api_key:
            self.after(0, self.update_test_status, "⚠️ 请先输入 Gemini API Key", "orange")
            return

        proxies = { "http": proxy_url, "https": proxy_url } if proxy_url else None

        # 测试网络连接
        try:
            # 首先测试基本网络连接
            test_url = "https://httpbin.org/get" if not proxy_url else "https://www.google.com"
            response = requests.get(test_url, proxies=proxies, timeout=10)

            if response.status_code == 200:
                # 如果基本连接成功，尝试测试 Gemini API
                try:
                    import google.generativeai as genai
                    genai.configure(api_key=api_key)

                    # 配置代理（如果有）
                    if proxy_url:
                        import os
                        os.environ['HTTP_PROXY'] = proxy_url
                        os.environ['HTTPS_PROXY'] = proxy_url

                    # 创建模型并测试
                    model = genai.GenerativeModel('gemini-pro')
                    test_response = model.generate_content("Hello", request_options={"timeout": 10})

                    if test_response.text:
                        self.after(0, self.update_test_status, "✅ API连接成功！", "green")
                    else:
                        self.after(0, self.update_test_status, "⚠️ API响应异常", "orange")

                except Exception as api_error:
                    if "API_KEY" in str(api_error).upper():
                        self.after(0, self.update_test_status, "❌ API Key 无效", "red")
                    elif "QUOTA" in str(api_error).upper():
                        self.after(0, self.update_test_status, "⚠️ API 配额不足", "orange")
                    else:
                        self.after(0, self.update_test_status, f"❌ API连接失败: {type(api_error).__name__}", "red")
            else:
                self.after(0, self.update_test_status, f"❌ 网络连接失败 (状态码: {response.status_code})", "red")

        except requests.exceptions.ProxyError:
            self.after(0, self.update_test_status, "❌ 代理服务器连接失败", "red")
        except requests.exceptions.Timeout:
            self.after(0, self.update_test_status, "❌ 连接超时", "red")
        except requests.exceptions.ConnectionError:
            self.after(0, self.update_test_status, "❌ 网络连接错误", "red")
        except Exception as e:
            self.after(0, self.update_test_status, f"❌ 连接失败: {type(e).__name__}", "red")

    def update_test_status(self, text, color):
        self.test_status_label.configure(text=text, text_color=color)
        self.test_button.configure(state="normal")

class AIStreamWindow(ctk.CTkToplevel):
    def __init__(self, master, title_text):
        super().__init__(master)
        self.title("🤖 AI 正在思考...")
        self.geometry("600x400")
        self.transient(master); self.grab_set(); self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.grid_rowconfigure(1, weight=1); self.grid_columnconfigure(0, weight=1)
        self.title_label = ctk.CTkLabel(self, text=f"正在处理: “{textwrap.shorten(title_text, width=50)}”", font=ctk.CTkFont(size=16, weight="bold")); self.title_label.grid(row=0, column=0, padx=20, pady=15)
        self.textbox = ctk.CTkTextbox(self, wrap="word", font=ctk.CTkFont(size=14)); self.textbox.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew"); self.textbox.insert("1.0", "AI正在连接并生成内容，请稍候...\n\n"); self.textbox.configure(state="disabled"); self.is_closed = False
    def update_content(self, chunk):
        if self.is_closed or not self.winfo_exists(): return
        self.textbox.configure(state="normal"); self.textbox.insert(ctk.END, chunk); self.textbox.see(ctk.END); self.textbox.configure(state="disabled"); self.update_idletasks()
    def on_close(self): self.is_closed = True; self.destroy()

class AIResponseWindow(ctk.CTkToplevel):
    def __init__(self, master, title_text, content_text):
        super().__init__(master)
        self.title("🤖 AI 创意分析报告"); self.geometry("700x600"); self.transient(master); self.grab_set(); self.grid_rowconfigure(1, weight=1); self.grid_columnconfigure(0, weight=1)
        self.title_label = ctk.CTkLabel(self, text=f"关于 “{textwrap.shorten(title_text, width=50)}”", font=ctk.CTkFont(size=18, weight="bold")); self.title_label.grid(row=0, column=0, padx=20, pady=15)
        self.textbox = ctk.CTkTextbox(self, wrap="word", font=ctk.CTkFont(size=14)); self.textbox.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew"); self.textbox.insert("1.0", content_text); self.textbox.configure(state="disabled")

class GraphViewWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master); self.master = master; self.title("想法图谱"); self.geometry("1000x800"); self.transient(master); self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.fig, self.ax = plt.subplots(facecolor="#2B2B2B"); self.canvas = FigureCanvasTkAgg(self.fig, master=self); self.draw_graph(); self.canvas.get_tk_widget().pack(side=ctk.TOP, fill=ctk.BOTH, expand=True)
    def draw_graph(self):
        self.ax.clear(); G = nx.Graph(); active_ideas = [idea for idea in self.master.ideas if not idea.get('is_deleted', False)]
        for idea in active_ideas: G.add_node(idea['id'], label=idea['core_idea'][:10]+"...", status=idea.get('status', DEFAULT_STATUS))
        tags_to_ideas = {}; from itertools import combinations
        for idea in active_ideas:
            for tag in idea.get('keywords', []):
                if tag not in tags_to_ideas: tags_to_ideas[tag] = []
                tags_to_ideas[tag].append(idea['id'])
        for tag in tags_to_ideas:
            if len(tags_to_ideas[tag]) > 1:
                for u, v in combinations(tags_to_ideas[tag], 2):
                    if u != v: G.add_edge(u, v, type='tag')
        for idea in active_ideas:
            for link in idea.get('linked_ideas', []):
                if G.has_node(link['id']) and G.has_node(idea['id']):
                     if idea['id'] != link['id']: G.add_edge(idea['id'], link['id'], type='manual')
        if not G.nodes(): self.ax.text(0.5, 0.5, "没有足够的想法来构建图谱", color="white", ha="center", va="center"); self.canvas.draw(); return
        pos = nx.spring_layout(G, k=0.7, iterations=50); node_colors = [STATUS_COLORS.get(G.nodes[node]['status'], '#cccccc') for node in G.nodes]; labels = nx.get_node_attributes(G, 'label'); edge_colors = ['#fca311' if G.edges[e]['type'] == 'manual' else '#6c757d' for e in G.edges]; edge_widths = [2.0 if G.edges[e]['type'] == 'manual' else 0.7 for e in G.edges]
        nx.draw_networkx_nodes(G, pos, ax=self.ax, node_size=2200, node_color=node_colors, alpha=0.9, node_shape='o'); nx.draw_networkx_edges(G, pos, ax=self.ax, edge_color=edge_colors, width=edge_widths, alpha=0.7); nx.draw_networkx_labels(G, pos, ax=self.ax, labels=labels, font_size=9, font_color='#ffffff', font_weight='bold'); self.ax.set_facecolor("#2B2B2B"); self.fig.tight_layout(); self.canvas.draw()
    def on_close(self): plt.close(self.fig); self.master.graph_window = None; self.destroy()

class LinkerWindow(ctk.CTkToplevel):
    def __init__(self, master, source_idea_id):
        super().__init__(master); self.master = master; self.source_idea_id = source_idea_id; self.title("手动链接到..."); self.geometry("600x500"); self.transient(master); self.grab_set(); self.grid_columnconfigure(0, weight=1); self.grid_rowconfigure(0, weight=1); self.idea_list_frame = ctk.CTkScrollableFrame(self, label_text="选择要链接的想法"); self.idea_list_frame.grid(row=0, column=0, padx=15, pady=10, sticky="nsew"); self.confirm_button = ctk.CTkButton(self, text="确认链接", command=self.confirm_links); self.confirm_button.grid(row=1, column=0, padx=15, pady=10, sticky="ew"); self.link_checkboxes = {}; self.populate_idea_list()
    def populate_idea_list(self):
        source_idea = next((i for i in self.master.ideas if i['id'] == self.source_idea_id), None);
        if not source_idea: return
        existing_links = [link['id'] for link in source_idea.get('linked_ideas', [])]
        for idea in self.master.ideas:
            if idea['id'] == self.source_idea_id or idea.get('is_deleted', False) or idea['id'] in existing_links: continue
            var = ctk.StringVar(value=""); cb = ctk.CTkCheckBox(self.idea_list_frame, text=idea['core_idea'], variable=var, onvalue=idea['id'], offvalue=""); cb.pack(anchor="w", padx=10, pady=5); self.link_checkboxes[idea['id']] = var
    def confirm_links(self):
        selected_ids = [var.get() for var in self.link_checkboxes.values() if var.get()]
        if selected_ids: self.master.add_manual_links(self.source_idea_id, selected_ids)
        self.destroy()

class TagManagerWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master); self.master = master; self.title("标签管理器"); self.geometry("700x550"); self.transient(master); self.grab_set(); self.grid_columnconfigure(0, weight=1); self.grid_columnconfigure(1, weight=1); self.grid_rowconfigure(0, weight=1); self.left_frame = ctk.CTkFrame(self); self.left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew"); self.left_frame.grid_rowconfigure(0, weight=1); self.tag_list_frame = ctk.CTkScrollableFrame(self.left_frame, label_text="所有标签"); self.tag_list_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew"); self.right_frame = ctk.CTkFrame(self); self.right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew"); ctk.CTkLabel(self.right_frame, text="重命名选中标签:", font=ctk.CTkFont(weight="bold")).pack(padx=20, pady=(10, 5), anchor="w"); self.rename_entry = ctk.CTkEntry(self.right_frame, placeholder_text="输入新名称..."); self.rename_entry.pack(padx=20, pady=5, fill="x"); self.rename_button = ctk.CTkButton(self.right_frame, text="确认重命名", command=self.rename_tag); self.rename_button.pack(padx=20, pady=5, fill="x"); ctk.CTkLabel(self.right_frame, text="合并标签:", font=ctk.CTkFont(weight="bold")).pack(padx=20, pady=(20, 5), anchor="w"); self.merge_source_label = ctk.CTkLabel(self.right_frame, text="将: (在左侧选择)", wraplength=180); self.merge_source_label.pack(padx=20, pady=5, anchor="w"); ctk.CTkLabel(self.right_frame, text="合并到:").pack(padx=20, pady=5, anchor="w"); self.merge_target_combo = ctk.CTkComboBox(self.right_frame, values=["选择目标标签"]); self.merge_target_combo.pack(padx=20, pady=5, fill="x"); self.merge_button = ctk.CTkButton(self.right_frame, text="确认合并", command=self.merge_tags); self.merge_button.pack(padx=20, pady=5, fill="x"); ctk.CTkLabel(self.right_frame, text="删除选中标签:", font=ctk.CTkFont(weight="bold")).pack(padx=20, pady=(20, 5), anchor="w"); self.delete_info_label = ctk.CTkLabel(self.right_frame, text="(此操作仅从所有想法中移除该标签)", wraplength=180, text_color="gray"); self.delete_info_label.pack(padx=20, pady=2, anchor="w"); self.delete_button = ctk.CTkButton(self.right_frame, text="确认删除", fg_color="#D32F2F", hover_color="#B71C1C", command=self.delete_tag); self.delete_button.pack(padx=20, pady=5, fill="x"); self.selected_tags = []; self.all_tags = self.master.get_all_tags(); self.merge_target_combo.configure(values=self.all_tags); self.refresh_tag_list()
    def refresh_tag_list(self):
        for widget in self.tag_list_frame.winfo_children(): widget.destroy()
        self.all_tags = self.master.get_all_tags(); self.tag_buttons = {}
        for tag in self.all_tags:
            count = sum(1 for idea in self.master.ideas if tag in idea.get('keywords', [])); btn_text = f"{tag} ({count})"; btn = ctk.CTkButton(self.tag_list_frame, text=btn_text, fg_color="transparent", anchor="w", command=partial(self.select_tag, tag)); btn.pack(fill="x", padx=5, pady=2); self.tag_buttons[tag] = btn
    def select_tag(self, tag):
        if tag in self.selected_tags: self.selected_tags.remove(tag); self.tag_buttons[tag].configure(fg_color="transparent")
        else: self.selected_tags.append(tag); self.tag_buttons[tag].configure(fg_color="#3B8ED0")
        self.merge_source_label.configure(text=f"将: {', '.join(self.selected_tags)}")
    def rename_tag(self):
        if len(self.selected_tags) != 1: return
        old_name = self.selected_tags[0]; new_name = self.rename_entry.get().strip()
        if not new_name or new_name == old_name: return
        for idea in self.master.ideas:
            if 'keywords' in idea and old_name in idea['keywords']:
                idea['keywords'].remove(old_name)
                if new_name not in idea['keywords']:
                    idea['keywords'].append(new_name)
        self.post_action_update(); self.rename_entry.delete(0, 'end')
    def merge_tags(self):
        target_tag = self.merge_target_combo.get()
        if not self.selected_tags or not target_tag or "选择" in target_tag: return
        for idea in self.master.ideas:
            if 'keywords' in idea:
                idea_tags = set(idea['keywords'])
                if any(src_tag in idea_tags for src_tag in self.selected_tags):
                    idea_tags.add(target_tag); idea_tags.difference_update(self.selected_tags); idea['keywords'] = list(idea_tags)
        self.post_action_update()
    def delete_tag(self):
        if not self.selected_tags: return
        for idea in self.master.ideas:
            if 'keywords' in idea:
                idea['keywords'] = [k for k in idea['keywords'] if k not in self.selected_tags]
        self.post_action_update()
    def post_action_update(self):
        self.master.save_ideas(); self.master.update_tag_selection_area(); self.master.update_keyword_combos(); self.selected_tags = []; self.all_tags = self.master.get_all_tags(); self.merge_target_combo.configure(values=self.all_tags); self.refresh_tag_list(); self.master.update_graph()

class MarkdownPreviewWindow(ctk.CTkToplevel):
    def __init__(self, master, title_text, content_text):
        super().__init__(master)
        self.title("📝 Markdown 预览")
        self.geometry("800x600")
        self.transient(master)
        self.grab_set()

        self.grid_rowconfigure(1, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # 标题
        title_label = ctk.CTkLabel(
            self,
            text=f"预览: {title_text}",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=15)

        # 预览内容
        self.preview_textbox = ctk.CTkTextbox(
            self,
            wrap="word",
            font=ctk.CTkFont(size=14)
        )
        self.preview_textbox.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")

        # 简单的Markdown渲染
        rendered_content = self.simple_markdown_render(content_text)
        self.preview_textbox.insert("1.0", rendered_content)
        self.preview_textbox.configure(state="disabled")

    def simple_markdown_render(self, text):
        """简单的Markdown渲染"""
        lines = text.split('\n')
        rendered_lines = []

        for line in lines:
            # 标题处理
            if line.startswith('# '):
                rendered_lines.append(f"【标题1】{line[2:]}")
            elif line.startswith('## '):
                rendered_lines.append(f"【标题2】{line[3:]}")
            elif line.startswith('### '):
                rendered_lines.append(f"【标题3】{line[4:]}")
            # 粗体处理
            elif '**' in line:
                line = line.replace('**', '【粗体】')
                rendered_lines.append(line)
            # 斜体处理
            elif '*' in line:
                line = line.replace('*', '【斜体】')
                rendered_lines.append(line)
            # 列表处理
            elif line.startswith('- ') or line.startswith('* '):
                rendered_lines.append(f"  • {line[2:]}")
            elif line.startswith('1. ') or line.startswith('2. ') or line.startswith('3. '):
                rendered_lines.append(f"  {line}")
            else:
                rendered_lines.append(line)

        return '\n'.join(rendered_lines)

class ExportDialog(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.title("📤 导出想法")
        self.geometry("500x400")
        self.transient(master)
        self.grab_set()

        self.grid_rowconfigure(2, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # 标题
        title_label = ctk.CTkLabel(self, text="选择导出格式和内容", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.grid(row=0, column=0, padx=20, pady=20)

        # 导出格式选择
        format_frame = ctk.CTkFrame(self)
        format_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")

        ctk.CTkLabel(format_frame, text="导出格式:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.format_var = ctk.StringVar(value="markdown")
        format_options = [
            ("Markdown (.md)", "markdown"),
            ("JSON (.json)", "json"),
            ("纯文本 (.txt)", "text"),
            ("CSV (.csv)", "csv")
        ]

        for text, value in format_options:
            radio = ctk.CTkRadioButton(format_frame, text=text, variable=self.format_var, value=value)
            radio.pack(anchor="w", padx=30, pady=2)

        # 内容选择
        content_frame = ctk.CTkFrame(self)
        content_frame.grid(row=2, column=0, padx=20, pady=10, sticky="nsew")

        ctk.CTkLabel(content_frame, text="导出内容:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.export_all_var = ctk.BooleanVar(value=True)
        self.export_active_var = ctk.BooleanVar(value=False)
        self.export_selected_var = ctk.BooleanVar(value=False)

        ctk.CTkCheckBox(content_frame, text="所有想法", variable=self.export_all_var).pack(anchor="w", padx=30, pady=2)
        ctk.CTkCheckBox(content_frame, text="仅活跃想法（未删除）", variable=self.export_active_var).pack(anchor="w", padx=30, pady=2)
        if self.master.current_idea_id:
            ctk.CTkCheckBox(content_frame, text="仅当前选中想法", variable=self.export_selected_var).pack(anchor="w", padx=30, pady=2)

        # 按钮
        button_frame = ctk.CTkFrame(self, fg_color="transparent")
        button_frame.grid(row=3, column=0, padx=20, pady=20, sticky="ew")
        button_frame.grid_columnconfigure(0, weight=1)

        export_button = ctk.CTkButton(button_frame, text="📤 开始导出", command=self.start_export, fg_color="#32CD32", hover_color="#228B22")
        export_button.grid(row=0, column=0, padx=(0, 10), sticky="e")

        cancel_button = ctk.CTkButton(button_frame, text="取消", command=self.destroy, fg_color="#DC143C", hover_color="#B22222")
        cancel_button.grid(row=0, column=1, sticky="e")

    def start_export(self):
        """开始导出过程"""
        format_type = self.format_var.get()

        # 确定要导出的想法
        ideas_to_export = []
        if self.export_selected_var.get() and self.master.current_idea_id:
            ideas_to_export = [idea for idea in self.master.ideas if idea['id'] == self.master.current_idea_id]
        elif self.export_active_var.get():
            ideas_to_export = [idea for idea in self.master.ideas if not idea.get('is_deleted', False)]
        else:  # export_all_var
            ideas_to_export = self.master.ideas

        if not ideas_to_export:
            self.master.show_status_message("❌ 没有可导出的想法", "red")
            return

        # 选择保存位置
        import tkinter.filedialog as fd
        file_extensions = {
            "markdown": [("Markdown files", "*.md")],
            "json": [("JSON files", "*.json")],
            "text": [("Text files", "*.txt")],
            "csv": [("CSV files", "*.csv")]
        }

        filename = fd.asksaveasfilename(
            title="保存导出文件",
            filetypes=file_extensions[format_type],
            defaultextension=f".{format_type if format_type != 'markdown' else 'md'}"
        )

        if filename:
            try:
                self.export_to_file(ideas_to_export, filename, format_type)
                self.master.show_status_message(f"✅ 已导出 {len(ideas_to_export)} 个想法", "green")
                self.destroy()
            except Exception as e:
                self.master.show_status_message(f"❌ 导出失败: {str(e)}", "red")

    def export_to_file(self, ideas, filename, format_type):
        """导出想法到文件"""
        if format_type == "markdown":
            self.export_markdown(ideas, filename)
        elif format_type == "json":
            self.export_json(ideas, filename)
        elif format_type == "text":
            self.export_text(ideas, filename)
        elif format_type == "csv":
            self.export_csv(ideas, filename)

    def export_markdown(self, ideas, filename):
        """导出为Markdown格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# 我的想法集合\n\n")
            f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"想法数量: {len(ideas)}\n\n")

            for idea in ideas:
                f.write(f"## {idea.get('core_idea', '无标题')}\n\n")
                f.write(f"**状态**: {idea.get('status', '未知')}\n\n")

                if idea.get('keywords'):
                    f.write(f"**标签**: {', '.join(idea['keywords'])}\n\n")

                if idea.get('context'):
                    f.write(f"### 背景/来源\n{idea['context']}\n\n")

                if idea.get('application'):
                    f.write(f"### 潜在应用\n{idea['application']}\n\n")

                if idea.get('questions'):
                    f.write(f"### 相关问题\n{idea['questions']}\n\n")

                if idea.get('linked_ideas'):
                    f.write("### 相关链接\n")
                    for link in idea['linked_ideas']:
                        linked_idea = next((i for i in self.master.ideas if i['id'] == link['id']), None)
                        if linked_idea:
                            f.write(f"- {linked_idea.get('core_idea', '未知想法')}\n")
                    f.write("\n")

                f.write("---\n\n")

    def export_json(self, ideas, filename):
        """导出为JSON格式"""
        export_data = {
            "export_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "total_ideas": len(ideas),
            "ideas": ideas
        }
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

    def export_text(self, ideas, filename):
        """导出为纯文本格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("我的想法集合\n")
            f.write("=" * 20 + "\n\n")
            f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"想法数量: {len(ideas)}\n\n")

            for i, idea in enumerate(ideas, 1):
                f.write(f"{i}. {idea.get('core_idea', '无标题')}\n")
                f.write("-" * 40 + "\n")
                f.write(f"状态: {idea.get('status', '未知')}\n")

                if idea.get('keywords'):
                    f.write(f"标签: {', '.join(idea['keywords'])}\n")

                if idea.get('context'):
                    f.write(f"背景: {idea['context']}\n")

                if idea.get('application'):
                    f.write(f"应用: {idea['application']}\n")

                if idea.get('questions'):
                    f.write(f"问题: {idea['questions']}\n")

                f.write("\n")

    def export_csv(self, ideas, filename):
        """导出为CSV格式"""
        import csv
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['核心想法', '状态', '标签', '背景', '应用', '问题', '创建时间'])

            for idea in ideas:
                writer.writerow([
                    idea.get('core_idea', ''),
                    idea.get('status', ''),
                    ', '.join(idea.get('keywords', [])),
                    idea.get('context', ''),
                    idea.get('application', ''),
                    idea.get('questions', ''),
                    time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(idea.get('id', 0) / 1000))
                ])

class RecycleBinWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master); self.master = master; self.title("回收站"); self.geometry("600x500"); self.transient(master); self.grab_set(); self.grid_columnconfigure(0, weight=1); self.grid_rowconfigure(0, weight=1); self.selected_trash_id = None; self.trash_list_frame = ctk.CTkScrollableFrame(self, label_text="已删除的想法"); self.trash_list_frame.grid(row=0, column=0, columnspan=2, padx=15, pady=10, sticky="nsew"); self.button_frame = ctk.CTkFrame(self, fg_color="transparent"); self.button_frame.grid(row=1, column=0, columnspan=2, padx=15, pady=10, sticky="ew"); self.button_frame.grid_columnconfigure(0, weight=1); self.restore_button = ctk.CTkButton(self.button_frame, text="恢复选中", command=self.restore_selected); self.restore_button.grid(row=0, column=0, padx=(0, 5), sticky="ew"); self.perm_delete_button = ctk.CTkButton(self.button_frame, text="彻底删除", fg_color="#D32F2F", hover_color="#B71C1C", command=self.delete_selected_permanently); self.perm_delete_button.grid(row=0, column=1, padx=(5, 0), sticky="ew"); self.refresh_trash_list()
    def refresh_trash_list(self):
        for widget in self.trash_list_frame.winfo_children(): widget.destroy()
        deleted_ideas = [idea for idea in self.master.ideas if idea.get('is_deleted', False)]
        if not deleted_ideas: ctk.CTkLabel(self.trash_list_frame, text="回收站是空的").pack(expand=True); return
        for idea in deleted_ideas:
            idea_id = idea.get("id"); core_idea = idea.get("core_idea", "无标题想法"); btn = ctk.CTkButton(self.trash_list_frame, text=core_idea, fg_color="transparent", anchor="w", command=lambda id=idea_id: self.select_trash_item(id)); btn.pack(fill="x", padx=5, pady=2)
    def select_trash_item(self, idea_id): self.selected_trash_id = idea_id
    def restore_selected(self):
        if not self.selected_trash_id: return
        idea = next((item for item in self.master.ideas if item["id"] == self.selected_trash_id), None)
        if idea: idea['is_deleted'] = False; self.master.save_ideas(); self.master.refresh_idea_list(); self.refresh_trash_list(); self.selected_trash_id = None; self.master.update_graph()
    def delete_selected_permanently(self):
        if not self.selected_trash_id: return
        self.master.ideas = [idea for idea in self.master.ideas if idea["id"] != self.selected_trash_id]; self.master.save_ideas(); self.refresh_trash_list(); self.selected_trash_id = None; self.master.update_graph()

# ==============================================================================
# ---- IDEA NODE DATA MODEL ----
# ==============================================================================
class IdeaNode:
    """
    想法节点 - 新一代的思维数据结构
    这是我们第二大脑系统的核心数据模型，支持AI分析和语义连接
    """
    def __init__(self, content="", node_id=None):
        import uuid
        from datetime import datetime

        # 基础标识
        self.id = node_id or str(uuid.uuid4())
        self.content = content  # 支持Markdown的主要内容
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()

        # 标签系统
        self.tags = []  # 用户手动添加的标签

        # 连接关系 - 这是思维网络的核心
        self.connections = []  # 格式: [{"nodeId": "xxx", "relationType": "引用|反驳|支持|扩展", "strength": 0.8}]

        # AI分析结果 - 这是智能化的关键
        self.ai_analysis = {
            "core_idea": "",           # AI提炼的核心观点
            "potential_questions": [], # AI生成的可延展问题
            "suggested_tags": [],      # AI建议的标签
            "sentiment": "中立",       # 情感分析：积极|中立|批判性|消极
            "complexity_level": 1,     # 复杂度等级 1-5
            "key_concepts": [],        # 提取的关键概念
            "themes": [],              # 识别的主题
            "last_analyzed_at": None,  # 上次分析时间
            "confidence_score": 0.0    # AI分析的置信度
        }

        # 语义特征 - 用于连接发现
        self.semantic_features = {
            "embedding_vector": [],    # 文本嵌入向量
            "similarity_cache": {},    # 与其他节点的相似度缓存
            "cluster_id": None,        # 所属聚类ID
            "importance_score": 0.0    # 重要性评分
        }

        # 元数据
        self.metadata = {
            "word_count": 0,
            "access_count": 0,
            "last_accessed": None,
            "source": "manual",        # manual|import|ai_generated
            "version": 1,
            "is_deleted": False,
            "node_type": "idea"        # idea|note|quote|reference|insight
        }

    def to_dict(self):
        """转换为字典格式，用于JSON序列化"""
        return {
            "id": self.id,
            "content": self.content,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "tags": self.tags,
            "connections": self.connections,
            "ai_analysis": self.ai_analysis,
            "semantic_features": self.semantic_features,
            "metadata": self.metadata
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建IdeaNode实例"""
        node = cls(content=data.get("content", ""), node_id=data.get("id"))
        node.created_at = data.get("created_at", node.created_at)
        node.updated_at = data.get("updated_at", node.updated_at)
        node.tags = data.get("tags", [])
        node.connections = data.get("connections", [])
        node.ai_analysis = data.get("ai_analysis", node.ai_analysis)
        node.semantic_features = data.get("semantic_features", node.semantic_features)
        node.metadata = data.get("metadata", node.metadata)
        return node

    def add_connection(self, target_node_id, relation_type, strength=0.5):
        """添加与其他节点的连接"""
        from datetime import datetime
        connection = {
            "nodeId": target_node_id,
            "relationType": relation_type,
            "strength": strength,
            "created_at": datetime.now().isoformat()
        }
        self.connections.append(connection)
        self.updated_at = datetime.now().isoformat()

    def update_ai_analysis(self, analysis_result):
        """更新AI分析结果"""
        from datetime import datetime
        self.ai_analysis.update(analysis_result)
        self.ai_analysis["last_analyzed_at"] = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()

    def get_connection_strength(self, target_node_id):
        """获取与指定节点的连接强度"""
        for conn in self.connections:
            if conn["nodeId"] == target_node_id:
                return conn["strength"]
        return 0.0

class IdeaNodeManager:
    """
    想法节点管理器 - 负责IdeaNode的创建、转换和管理
    """
    @staticmethod
    def convert_legacy_idea_to_node(legacy_idea):
        """将旧格式的想法转换为新的IdeaNode"""
        # 创建新的IdeaNode
        node = IdeaNode(content=legacy_idea.get("core_idea", ""))

        # 设置基础信息
        node.id = str(legacy_idea.get("id", node.id))
        node.created_at = legacy_idea.get("created_at", node.created_at)
        node.updated_at = legacy_idea.get("updated_at", node.updated_at)

        # 转换标签
        node.tags = legacy_idea.get("keywords", [])

        # 构建内容（合并多个字段）
        content_parts = []
        if legacy_idea.get("core_idea"):
            content_parts.append(f"# {legacy_idea['core_idea']}")
        if legacy_idea.get("context"):
            content_parts.append(f"## 背景\n{legacy_idea['context']}")
        if legacy_idea.get("application"):
            content_parts.append(f"## 应用\n{legacy_idea['application']}")
        if legacy_idea.get("questions"):
            content_parts.append(f"## 问题\n{legacy_idea['questions']}")

        node.content = "\n\n".join(content_parts)

        # 设置元数据
        node.metadata.update({
            "word_count": len(node.content.split()),
            "source": "legacy_import",
            "node_type": "idea",
            "is_deleted": legacy_idea.get("is_deleted", False)
        })

        # 如果有现有的AI分析数据，尝试转换
        if legacy_idea.get("profile"):
            profile = legacy_idea["profile"]
            node.ai_analysis["core_idea"] = profile.get("description", "")

        return node

    @staticmethod
    def convert_node_to_legacy_idea(node):
        """将IdeaNode转换回旧格式（用于兼容性）"""
        # 从content中提取核心想法（第一行标题）
        lines = node.content.split('\n')
        core_idea = lines[0].replace('# ', '') if lines else ""

        return {
            "id": node.id,
            "core_idea": core_idea,
            "context": "",  # 可以从content中解析
            "application": "",
            "questions": "",
            "keywords": node.tags,
            "created_at": node.created_at,
            "updated_at": node.updated_at,
            "is_deleted": node.metadata.get("is_deleted", False),
            "status": "构思中",
            "category": "想法"
        }

class ThinkingEngine:
    """
    思维引擎 - 负责AI分析和智能处理
    这是我们第二大脑系统的核心思考模块
    """
    def __init__(self, api_key=None, proxy_url=None):
        self.api_key = api_key
        self.proxy_url = proxy_url

    async def analyze_idea_node(self, node_id, idea_nodes_dict):
        """
        分析想法节点 - 核心AI分析函数

        Args:
            node_id: 要分析的节点ID
            idea_nodes_dict: 所有想法节点的字典

        Returns:
            更新后的IdeaNode对象
        """
        if node_id not in idea_nodes_dict:
            raise ValueError(f"Node {node_id} not found")

        node = idea_nodes_dict[node_id]

        # 构造分析提示词
        analysis_prompt = self._build_analysis_prompt(node)

        try:
            # 调用AI API进行分析
            analysis_result = await self._call_ai_api(analysis_prompt)

            # 解析AI返回的结果
            parsed_result = self._parse_ai_response(analysis_result)

            # 更新节点的AI分析数据
            node.update_ai_analysis(parsed_result)

            # 计算语义特征
            await self._compute_semantic_features(node)

            print(f"✅ 成功分析节点: {node_id}")
            return node

        except Exception as e:
            print(f"❌ 分析节点失败 {node_id}: {str(e)}")
            # 设置错误状态
            node.ai_analysis["last_analyzed_at"] = time.time()
            node.ai_analysis["confidence_score"] = 0.0
            raise e

    def _build_analysis_prompt(self, node):
        """构建AI分析提示词"""
        prompt = f"""
请深度分析以下想法内容，并以JSON格式返回分析结果：

内容：
{node.content}

现有标签：{', '.join(node.tags)}

请按以下JSON格式返回分析结果：
{{
    "core_idea": "提炼的核心观点（一句话概括）",
    "potential_questions": ["延展问题1", "延展问题2", "延展问题3"],
    "suggested_tags": ["建议标签1", "建议标签2", "建议标签3"],
    "sentiment": "积极|中立|批判性|消极",
    "complexity_level": 1-5的整数,
    "key_concepts": ["关键概念1", "关键概念2"],
    "themes": ["主题1", "主题2"],
    "confidence_score": 0.0-1.0的浮点数
}}

要求：
1. core_idea要简洁有力，抓住本质
2. potential_questions要具有启发性，能引导深入思考
3. suggested_tags要准确反映内容特征
4. sentiment要客观评估内容的情感倾向
5. complexity_level根据内容的复杂程度评分
6. key_concepts提取最重要的概念
7. themes识别内容涉及的主要主题
8. confidence_score反映分析的可靠程度
"""
        return prompt

    async def _call_ai_api(self, prompt):
        """调用AI API"""
        if not self.api_key:
            raise ValueError("API密钥未配置")

        # 这里使用现有的AI配置逻辑
        import openai

        # 配置API
        if self.proxy_url:
            openai.api_base = self.proxy_url
        openai.api_key = self.api_key

        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一位专业的思维分析师，擅长提炼核心观点和发现深层连接。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            print(f"AI API调用失败: {e}")
            raise e

    def _parse_ai_response(self, response_text):
        """解析AI返回的JSON结果"""
        try:
            import json
            # 尝试提取JSON部分
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                result = json.loads(json_str)

                # 验证必要字段
                required_fields = ["core_idea", "potential_questions", "suggested_tags", "sentiment"]
                for field in required_fields:
                    if field not in result:
                        result[field] = "" if field in ["core_idea", "sentiment"] else []

                return result
            else:
                raise ValueError("无法找到有效的JSON格式")

        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            # 返回默认结构
            return {
                "core_idea": "分析失败",
                "potential_questions": [],
                "suggested_tags": [],
                "sentiment": "中立",
                "complexity_level": 1,
                "key_concepts": [],
                "themes": [],
                "confidence_score": 0.0
            }

    async def _compute_semantic_features(self, node):
        """计算语义特征（简化版本）"""
        # 这里可以实现文本嵌入向量计算
        # 暂时使用简单的特征计算
        content_length = len(node.content)
        word_count = len(node.content.split())

        node.semantic_features.update({
            "importance_score": min(word_count / 100.0, 1.0),  # 基于字数的重要性评分
            "last_computed": time.time()
        })

        node.metadata["word_count"] = word_count

    def analyze_idea_node_sync(self, node_id, idea_nodes_dict):
        """
        同步版本的想法节点分析 - 用于UI调用
        """
        if node_id not in idea_nodes_dict:
            raise ValueError(f"Node {node_id} not found")

        node = idea_nodes_dict[node_id]

        # 构造分析提示词
        analysis_prompt = self._build_analysis_prompt(node)

        try:
            # 调用同步版本的AI API
            analysis_result = self._call_ai_api_sync(analysis_prompt)

            # 解析AI返回的结果
            parsed_result = self._parse_ai_response(analysis_result)

            # 更新节点的AI分析数据
            node.update_ai_analysis(parsed_result)

            # 计算语义特征
            self._compute_semantic_features_sync(node)

            print(f"✅ 成功分析节点: {node_id}")
            return node

        except Exception as e:
            print(f"❌ 分析节点失败 {node_id}: {str(e)}")
            # 设置错误状态
            node.ai_analysis["last_analyzed_at"] = time.time()
            node.ai_analysis["confidence_score"] = 0.0
            raise e

    def _call_ai_api_sync(self, prompt):
        """同步版本的AI API调用"""
        if not self.api_key:
            raise ValueError("API密钥未配置")

        # 使用现有的Gemini配置
        import google.generativeai as genai
        import os

        # 配置代理
        if self.proxy_url:
            os.environ['HTTP_PROXY'] = self.proxy_url
            os.environ['HTTPS_PROXY'] = self.proxy_url

        # 配置API
        genai.configure(api_key=self.api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')

        try:
            response = model.generate_content(prompt)
            return response.text

        except Exception as e:
            print(f"AI API调用失败: {e}")
            raise e

    def _compute_semantic_features_sync(self, node):
        """同步版本的语义特征计算"""
        content_length = len(node.content)
        word_count = len(node.content.split())

        node.semantic_features.update({
            "importance_score": min(word_count / 100.0, 1.0),
            "last_computed": time.time()
        })

        node.metadata["word_count"] = word_count

class ConnectionEngine:
    """
    连接引擎 - 负责发现和建立想法间的语义连接
    这是构建思维网络的核心模块
    """
    def __init__(self):
        self.connection_threshold = 0.6  # 连接强度阈值

    def discover_connections(self, target_node, all_nodes):
        """
        发现与目标节点的潜在连接

        Args:
            target_node: 目标IdeaNode
            all_nodes: 所有IdeaNode的字典 {node_id: IdeaNode}

        Returns:
            list: 发现的连接列表 [{"node_id": str, "relation_type": str, "strength": float, "reason": str}]
        """
        discovered_connections = []

        for node_id, node in all_nodes.items():
            if node_id == target_node.id or node.metadata.get("is_deleted"):
                continue

            # 计算多维度连接强度
            connections = self._analyze_node_connections(target_node, node)

            for connection in connections:
                if connection["strength"] >= self.connection_threshold:
                    discovered_connections.append({
                        "node_id": node_id,
                        "relation_type": connection["type"],
                        "strength": connection["strength"],
                        "reason": connection["reason"]
                    })

        # 按连接强度排序
        discovered_connections.sort(key=lambda x: x["strength"], reverse=True)
        return discovered_connections[:10]  # 返回前10个最强连接

    def _analyze_node_connections(self, node1, node2):
        """分析两个节点间的连接关系"""
        connections = []

        # 1. 标签重叠分析
        tag_connection = self._analyze_tag_overlap(node1, node2)
        if tag_connection:
            connections.append(tag_connection)

        # 2. 概念相似性分析
        concept_connection = self._analyze_concept_similarity(node1, node2)
        if concept_connection:
            connections.append(concept_connection)

        # 3. 主题关联分析
        theme_connection = self._analyze_theme_relation(node1, node2)
        if theme_connection:
            connections.append(theme_connection)

        # 4. 内容语义分析
        semantic_connection = self._analyze_semantic_similarity(node1, node2)
        if semantic_connection:
            connections.append(semantic_connection)

        return connections

    def _analyze_tag_overlap(self, node1, node2):
        """分析标签重叠度"""
        tags1 = set(node1.tags + node1.ai_analysis.get("suggested_tags", []))
        tags2 = set(node2.tags + node2.ai_analysis.get("suggested_tags", []))

        if not tags1 or not tags2:
            return None

        overlap = tags1.intersection(tags2)
        union = tags1.union(tags2)

        if overlap:
            strength = len(overlap) / len(union)  # Jaccard相似度
            if strength > 0.2:
                return {
                    "type": "标签关联",
                    "strength": strength,
                    "reason": f"共享标签: {', '.join(list(overlap)[:3])}"
                }
        return None

    def _analyze_concept_similarity(self, node1, node2):
        """分析关键概念相似性"""
        concepts1 = set(node1.ai_analysis.get("key_concepts", []))
        concepts2 = set(node2.ai_analysis.get("key_concepts", []))

        if not concepts1 or not concepts2:
            return None

        overlap = concepts1.intersection(concepts2)
        if overlap:
            strength = len(overlap) / max(len(concepts1), len(concepts2))
            if strength > 0.3:
                return {
                    "type": "概念相似",
                    "strength": strength,
                    "reason": f"共同概念: {', '.join(list(overlap)[:2])}"
                }
        return None

    def _analyze_theme_relation(self, node1, node2):
        """分析主题关联性"""
        themes1 = set(node1.ai_analysis.get("themes", []))
        themes2 = set(node2.ai_analysis.get("themes", []))

        if not themes1 or not themes2:
            return None

        overlap = themes1.intersection(themes2)
        if overlap:
            strength = len(overlap) / min(len(themes1), len(themes2))
            if strength > 0.4:
                return {
                    "type": "主题关联",
                    "strength": strength,
                    "reason": f"相关主题: {', '.join(list(overlap)[:2])}"
                }
        return None

    def _analyze_semantic_similarity(self, node1, node2):
        """分析语义相似性（简化版本）"""
        # 基于内容长度和核心观点的简单相似性计算
        core1 = node1.ai_analysis.get("core_idea", "").lower()
        core2 = node2.ai_analysis.get("core_idea", "").lower()

        if not core1 or not core2:
            return None

        # 简单的词汇重叠计算
        words1 = set(core1.split())
        words2 = set(core2.split())

        if len(words1) < 3 or len(words2) < 3:
            return None

        overlap = words1.intersection(words2)
        if len(overlap) >= 2:
            strength = len(overlap) / max(len(words1), len(words2))
            if strength > 0.3:
                return {
                    "type": "语义相似",
                    "strength": strength,
                    "reason": f"相似表达: {', '.join(list(overlap)[:2])}"
                }
        return None

    def establish_connections(self, source_node, connections, all_nodes):
        """建立连接关系"""
        established = []

        for conn in connections:
            target_node_id = conn["node_id"]
            if target_node_id in all_nodes:
                # 添加连接到源节点
                source_node.add_connection(
                    target_node_id,
                    conn["relation_type"],
                    conn["strength"]
                )

                # 添加反向连接到目标节点
                target_node = all_nodes[target_node_id]
                target_node.add_connection(
                    source_node.id,
                    self._get_reverse_relation(conn["relation_type"]),
                    conn["strength"]
                )

                established.append(conn)

        return established

    def _get_reverse_relation(self, relation_type):
        """获取反向关系类型"""
        reverse_map = {
            "标签关联": "标签关联",
            "概念相似": "概念相似",
            "主题关联": "主题关联",
            "语义相似": "语义相似",
            "引用": "被引用",
            "支持": "被支持",
            "反驳": "被反驳",
            "扩展": "被扩展"
        }
        return reverse_map.get(relation_type, relation_type)

class EmergenceEngine:
    """
    涌现引擎 - 从想法网络中发现新的模式和洞察
    这是第二大脑系统的智慧涌现模块
    """
    def __init__(self):
        self.min_cluster_size = 3  # 最小聚类大小
        self.insight_threshold = 0.8  # 洞察置信度阈值

    def discover_emergent_insights(self, idea_nodes):
        """
        发现涌现洞察

        Args:
            idea_nodes: 所有想法节点的字典

        Returns:
            dict: 发现的洞察 {"clusters": [], "patterns": [], "gaps": [], "trends": []}
        """
        insights = {
            "clusters": [],      # 想法聚类
            "patterns": [],      # 模式发现
            "gaps": [],          # 知识空白
            "trends": [],        # 趋势分析
            "bridges": []        # 桥接机会
        }

        # 1. 发现想法聚类
        insights["clusters"] = self._discover_idea_clusters(idea_nodes)

        # 2. 识别思维模式
        insights["patterns"] = self._identify_thinking_patterns(idea_nodes)

        # 3. 发现知识空白
        insights["gaps"] = self._identify_knowledge_gaps(idea_nodes)

        # 4. 分析发展趋势
        insights["trends"] = self._analyze_development_trends(idea_nodes)

        # 5. 发现桥接机会
        insights["bridges"] = self._discover_bridge_opportunities(idea_nodes)

        return insights

    def _discover_idea_clusters(self, idea_nodes):
        """发现想法聚类"""
        clusters = []
        processed_nodes = set()

        for node_id, node in idea_nodes.items():
            if node_id in processed_nodes or node.metadata.get("is_deleted"):
                continue

            # 基于连接关系构建聚类
            cluster = self._build_cluster_from_node(node, idea_nodes, processed_nodes)

            if len(cluster["nodes"]) >= self.min_cluster_size:
                cluster["insights"] = self._analyze_cluster_insights(cluster, idea_nodes)
                clusters.append(cluster)

        return sorted(clusters, key=lambda x: x["strength"], reverse=True)

    def _build_cluster_from_node(self, start_node, all_nodes, processed_nodes):
        """从节点构建聚类"""
        cluster_nodes = [start_node.id]
        processed_nodes.add(start_node.id)

        # 递归添加强连接的节点
        to_process = [start_node.id]
        total_strength = 0

        while to_process:
            current_id = to_process.pop(0)
            current_node = all_nodes[current_id]

            for connection in current_node.connections:
                target_id = connection["nodeId"]
                if (target_id not in processed_nodes and
                    target_id in all_nodes and
                    connection["strength"] > 0.6):

                    cluster_nodes.append(target_id)
                    processed_nodes.add(target_id)
                    to_process.append(target_id)
                    total_strength += connection["strength"]

        # 分析聚类主题
        cluster_themes = self._extract_cluster_themes(cluster_nodes, all_nodes)

        return {
            "nodes": cluster_nodes,
            "size": len(cluster_nodes),
            "strength": total_strength / max(len(cluster_nodes), 1),
            "themes": cluster_themes,
            "center_node": start_node.id
        }

    def _extract_cluster_themes(self, node_ids, all_nodes):
        """提取聚类主题"""
        all_themes = []
        all_concepts = []
        all_tags = []

        for node_id in node_ids:
            node = all_nodes[node_id]
            all_themes.extend(node.ai_analysis.get("themes", []))
            all_concepts.extend(node.ai_analysis.get("key_concepts", []))
            all_tags.extend(node.tags)

        # 统计频率
        from collections import Counter
        theme_counts = Counter(all_themes)
        concept_counts = Counter(all_concepts)
        tag_counts = Counter(all_tags)

        return {
            "dominant_themes": [theme for theme, count in theme_counts.most_common(3)],
            "key_concepts": [concept for concept, count in concept_counts.most_common(5)],
            "common_tags": [tag for tag, count in tag_counts.most_common(3)]
        }

    def _analyze_cluster_insights(self, cluster, all_nodes):
        """分析聚类洞察"""
        insights = []

        # 聚类规模洞察
        if cluster["size"] >= 5:
            insights.append({
                "type": "规模洞察",
                "content": f"发现了一个包含{cluster['size']}个想法的大型主题聚类",
                "confidence": 0.9
            })

        # 主题一致性洞察
        dominant_themes = cluster["themes"]["dominant_themes"]
        if len(dominant_themes) >= 2:
            insights.append({
                "type": "主题洞察",
                "content": f"该聚类围绕'{', '.join(dominant_themes[:2])}'等主题形成",
                "confidence": 0.8
            })

        # 概念密度洞察
        key_concepts = cluster["themes"]["key_concepts"]
        if len(key_concepts) >= 3:
            insights.append({
                "type": "概念洞察",
                "content": f"核心概念包括: {', '.join(key_concepts[:3])}",
                "confidence": 0.7
            })

        return insights

    def _identify_thinking_patterns(self, idea_nodes):
        """识别思维模式"""
        patterns = []

        # 分析情感模式
        sentiment_pattern = self._analyze_sentiment_patterns(idea_nodes)
        if sentiment_pattern:
            patterns.append(sentiment_pattern)

        # 分析复杂度模式
        complexity_pattern = self._analyze_complexity_patterns(idea_nodes)
        if complexity_pattern:
            patterns.append(complexity_pattern)

        # 分析时间模式
        temporal_pattern = self._analyze_temporal_patterns(idea_nodes)
        if temporal_pattern:
            patterns.append(temporal_pattern)

        return patterns

    def _analyze_sentiment_patterns(self, idea_nodes):
        """分析情感模式"""
        sentiments = []
        for node in idea_nodes.values():
            if not node.metadata.get("is_deleted"):
                sentiment = node.ai_analysis.get("sentiment", "中立")
                sentiments.append(sentiment)

        if not sentiments:
            return None

        from collections import Counter
        sentiment_counts = Counter(sentiments)
        dominant_sentiment = sentiment_counts.most_common(1)[0]

        if dominant_sentiment[1] / len(sentiments) > 0.6:
            return {
                "type": "情感模式",
                "pattern": f"思维倾向偏向{dominant_sentiment[0]}",
                "confidence": dominant_sentiment[1] / len(sentiments),
                "suggestion": self._get_sentiment_suggestion(dominant_sentiment[0])
            }
        return None

    def _get_sentiment_suggestion(self, dominant_sentiment):
        """获取情感模式建议"""
        suggestions = {
            "积极": "保持积极思维，可以尝试更多批判性思考来平衡视角",
            "批判性": "批判性思维很好，建议增加一些积极的解决方案思考",
            "中立": "中立的思考很平衡，可以尝试更多情感化的深度思考",
            "消极": "建议增加一些积极的思考角度，寻找解决方案和机会"
        }
        return suggestions.get(dominant_sentiment, "继续保持多元化的思考方式")

    def _analyze_complexity_patterns(self, idea_nodes):
        """分析复杂度模式"""
        complexities = []
        for node in idea_nodes.values():
            if not node.metadata.get("is_deleted"):
                complexity = node.ai_analysis.get("complexity_level", 1)
                complexities.append(complexity)

        if not complexities:
            return None

        avg_complexity = sum(complexities) / len(complexities)

        if avg_complexity > 3.5:
            return {
                "type": "复杂度模式",
                "pattern": f"思维复杂度较高 (平均: {avg_complexity:.1f}/5)",
                "confidence": 0.8,
                "suggestion": "高复杂度思维很好，建议偶尔加入一些简单直接的想法来平衡"
            }
        elif avg_complexity < 2.0:
            return {
                "type": "复杂度模式",
                "pattern": f"思维复杂度较低 (平均: {avg_complexity:.1f}/5)",
                "confidence": 0.8,
                "suggestion": "可以尝试更深入的思考，探索更复杂的概念和关系"
            }
        else:
            return {
                "type": "复杂度模式",
                "pattern": f"思维复杂度适中 (平均: {avg_complexity:.1f}/5)",
                "confidence": 0.7,
                "suggestion": "复杂度平衡很好，继续保持多层次的思考"
            }

    def _analyze_temporal_patterns(self, idea_nodes):
        """分析时间模式"""
        from datetime import datetime, timedelta
        import time

        # 获取最近30天的想法
        now = time.time()
        thirty_days_ago = now - (30 * 24 * 3600)

        recent_nodes = []
        for node in idea_nodes.values():
            if not node.metadata.get("is_deleted"):
                try:
                    created_time = datetime.fromisoformat(node.created_at).timestamp()
                    if created_time > thirty_days_ago:
                        recent_nodes.append(node)
                except:
                    continue

        if len(recent_nodes) < 3:
            return None

        # 分析创建频率
        if len(recent_nodes) > 10:
            return {
                "type": "时间模式",
                "pattern": f"最近30天创建了{len(recent_nodes)}个想法，思维活跃度很高",
                "confidence": 0.9,
                "suggestion": "保持这种思维活跃度，可以考虑定期整理和连接想法"
            }
        elif len(recent_nodes) < 5:
            return {
                "type": "时间模式",
                "pattern": f"最近30天创建了{len(recent_nodes)}个想法，思维活跃度较低",
                "confidence": 0.8,
                "suggestion": "建议增加思考和记录的频率，保持思维的连续性"
            }
        else:
            return {
                "type": "时间模式",
                "pattern": f"最近30天创建了{len(recent_nodes)}个想法，思维活跃度适中",
                "confidence": 0.7,
                "suggestion": "思维节奏很好，可以尝试更深入地分析现有想法"
            }

# ==============================================================================
# ---- PAGE MANAGER CLASS ----
# ==============================================================================
class PageManager:
    """页面管理器 - 处理多级页面导航"""
    def __init__(self):
        self.page_stack = []  # 页面栈，用于前进后退
        self.current_page = None
        self.page_history = []  # 完整的浏览历史

    def navigate_to(self, page_type, page_data=None):
        """导航到指定页面"""
        page = {
            "type": page_type,
            "data": page_data,
            "timestamp": time.time()
        }

        # 如果不是相同页面，则添加到栈中
        if not self.current_page or self.current_page["type"] != page_type or self.current_page.get("data") != page_data:
            if self.current_page:
                self.page_stack.append(self.current_page)
            self.current_page = page
            self.page_history.append(page)

        return page

    def can_go_back(self):
        """是否可以后退"""
        return len(self.page_stack) > 0

    def go_back(self):
        """后退到上一页"""
        if self.can_go_back():
            self.current_page = self.page_stack.pop()
            return self.current_page
        return None

    def get_breadcrumb(self):
        """获取面包屑导航"""
        breadcrumb = []
        for page in self.page_stack + [self.current_page] if self.current_page else []:
            if page["type"] == "idea_list":
                breadcrumb.append("💡 想法库")
            elif page["type"] == "category_list":
                breadcrumb.append(f"📁 {page['data']['name']}")
            elif page["type"] == "idea_detail":
                breadcrumb.append(f"📄 {page['data']['title'][:20]}...")
            elif page["type"] == "tag_view":
                breadcrumb.append(f"🏷️ {page['data']['tag']}")
        return breadcrumb

# ==============================================================================
# ---- MAIN APPLICATION CLASS ----
# ==============================================================================
class IdeaGeneratorApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("个人想法生成器 v4.3 - 终极修复版")
        self.geometry("1200x750")
        
        # Core App Variables
        self.ideas = []
        self.current_idea_id = None
        self.graph_window = None
        self.tag_checkboxes = {}
        self.api_key = None
        self.proxy_url = None
        self.status_label = None  # 用于显示保存状态
        self.auto_save_timer = None  # 自动保存定时器
        self.last_content_hash = None  # 用于检测内容变化
        self.tutorial_completed = False  # 教程完成状态

        # 初始化页面管理器
        self.page_manager = PageManager()

        # 初始化想法节点管理器
        self.node_manager = IdeaNodeManager()
        self.idea_nodes = {}  # 存储IdeaNode对象的字典 {node_id: IdeaNode}

        # 初始化思维引擎
        self.thinking_engine = ThinkingEngine()

        # 初始化连接引擎
        self.connection_engine = ConnectionEngine()

        # 初始化涌现引擎
        self.emergence_engine = EmergenceEngine()

        # 配置思维引擎
        self.update_thinking_engine_config()

        # Obsidian风格的动态布局状态
        self.layout_state = {
            "left_panel_visible": True,
            "right_panel_visible": True,
            "left_panel_width": 300,
            "right_panel_width": 350,
            "current_view_mode": "edit",  # edit, preview, graph
            "auto_hide_panels": False,
            "focus_mode": False,
            "last_interaction_time": 0
        }

        # Load Config and Data
        self.load_config()
        self.load_ideas()

        # Setup UI
        self._setup_main_grid()
        self._create_left_frame()
        self._create_middle_frame()
        self._create_right_frame()
        
        # Initial Population
        self.navigate_to_page("idea_list")  # 使用新的页面导航系统
        self.update_keyword_combos()
        self.update_tag_selection_area()

        # 初始化智能推荐
        self.update_smart_recommendations()

        # 设置快捷键
        self.setup_keyboard_shortcuts()

        # 检查是否需要显示教程
        self.after(500, self.check_tutorial_needed)

    def _setup_main_grid(self):
        self.grid_columnconfigure(0, weight=1, minsize=250)
        self.grid_columnconfigure(1, weight=3)
        self.grid_columnconfigure(2, weight=2, minsize=300)
        self.grid_rowconfigure(0, weight=1)

    def _create_left_frame(self):
        """创建左侧导航面板 - 应用清晰的视觉层次"""
        self.left_frame = ctk.CTkFrame(
            self,
            corner_radius=DesignSystem.SPACING_SM,
            fg_color=DesignSystem.SECONDARY_BG
        )
        self.left_frame.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=DesignSystem.SPACING_MD, sticky="nsew")
        self.left_frame.grid_rowconfigure(8, weight=1)

        # 主标题区域
        title_frame = ctk.CTkFrame(self.left_frame, fg_color="transparent")
        title_frame.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_LG, DesignSystem.SPACING_MD), sticky="ew")

        title_label = ctk.CTkLabel(
            title_frame,
            text="💭 第二大脑",
            font=ctk.CTkFont(size=DesignSystem.FONT_LARGE, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        title_label.pack()

        # 主要操作按钮 - Notion风格的主要CTA
        self.new_idea_button = ctk.CTkButton(
            self.left_frame,
            text="✨ 新建想法",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            command=self.clear_form,
            fg_color=DesignSystem.ACCENT_BLUE,
            hover_color=DesignSystem.ACCENT_BLUE_HOVER,
            height=36,
            corner_radius=DesignSystem.RADIUS_MD,
            border_width=0
        )
        self.new_idea_button.grid(row=1, column=0, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_MD), sticky="ew")

        self.graph_view_button = ctk.CTkButton(
            self.left_frame,
            text="� 图谱视图",
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.open_graph_view,
            fg_color="#4169E1",
            hover_color="#1E90FF"
        )
        self.graph_view_button.grid(row=1, column=0, padx=20, pady=5, sticky="ew")

        # 搜索框 - Notion风格的简洁搜索
        self.search_entry = ctk.CTkEntry(
            self.left_frame,
            placeholder_text="🔍 搜索想法...",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.CARD_BG,
            border_color=DesignSystem.BORDER_LIGHT,
            border_width=1,
            corner_radius=DesignSystem.RADIUS_MD,
            height=32
        )
        self.search_entry.grid(row=2, column=0, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_LG), sticky="ew")
        self.search_entry.bind("<KeyRelease>", self.filter_ideas)

        # 次要功能按钮组 - Notion风格的简洁按钮
        secondary_buttons = [
            ("🌐 图谱视图", self.open_graph_view),
            ("🏷️ 标签管理", self.open_tag_manager),
            ("📤 导出", self.open_export_dialog),
            ("🗑️ 回收站", self.open_recycle_bin_window),
            ("🎓 新手教程", self.open_tutorial_manually),
            ("⚙️ 设置", self.open_settings_window)
        ]

        for i, (text, command) in enumerate(secondary_buttons):
            button = ctk.CTkButton(
                self.left_frame,
                text=text,
                command=command,
                font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
                fg_color="transparent",
                hover_color=DesignSystem.HOVER_BG,
                text_color=DesignSystem.TEXT_SECONDARY,
                anchor="w",
                height=32,
                corner_radius=DesignSystem.RADIUS_MD,
                border_width=0
            )
            button.grid(row=3+i, column=0, padx=DesignSystem.SPACING_LG, pady=2, sticky="ew")

        # 分类筛选器
        category_filter_frame = ctk.CTkFrame(self.left_frame, fg_color="transparent")
        category_filter_frame.grid(row=9, column=0, padx=DesignSystem.SPACING_LG, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="ew")
        category_filter_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            category_filter_frame,
            text="分类:",
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=0, column=0, sticky="w")

        self.category_filter = ctk.CTkComboBox(
            category_filter_frame,
            values=["全部"] + CATEGORY_LIST,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.CARD_BG,
            border_color=DesignSystem.BORDER_LIGHT,
            border_width=1,
            height=28,
            command=self.filter_ideas_by_category
        )
        self.category_filter.grid(row=0, column=1, padx=(DesignSystem.SPACING_SM, 0), sticky="ew")
        self.category_filter.set("全部")

        # 想法库导航区域
        idea_nav_frame = ctk.CTkFrame(
            self.left_frame,
            fg_color=DesignSystem.CARD_BG,
            corner_radius=DesignSystem.RADIUS_MD,
            height=40
        )
        idea_nav_frame.grid(row=9, column=0, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_SM), sticky="ew")
        idea_nav_frame.grid_propagate(False)

        # 面包屑导航
        self.breadcrumb_frame = ctk.CTkFrame(idea_nav_frame, fg_color="transparent")
        self.breadcrumb_frame.pack(side="left", fill="x", expand=True, padx=DesignSystem.SPACING_SM, pady=DesignSystem.SPACING_XS)

        # 后退按钮
        self.back_button = ctk.CTkButton(
            idea_nav_frame,
            text="←",
            width=30,
            height=28,
            font=ctk.CTkFont(size=16),
            fg_color="transparent",
            hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
            command=self.navigate_back,
            state="disabled"
        )
        self.back_button.pack(side="right", padx=(0, DesignSystem.SPACING_SM))

        # 想法列表 - Notion风格的内容区域
        self.idea_list_frame = ctk.CTkScrollableFrame(
            self.left_frame,
            fg_color=DesignSystem.CARD_BG,
            corner_radius=DesignSystem.RADIUS_MD,
            border_width=1,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.idea_list_frame.grid(row=10, column=0, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_LG), sticky="nsew")

    def _create_middle_frame(self):
        """创建中间编辑区 - 应用清晰的信息层次和充足留白"""
        self.middle_frame = ctk.CTkFrame(
            self,
            corner_radius=DesignSystem.SPACING_SM,
            fg_color=DesignSystem.SECONDARY_BG
        )
        self.middle_frame.grid(row=0, column=1, padx=0, pady=DesignSystem.SPACING_MD, sticky="nsew")
        self.middle_frame.grid_columnconfigure(0, weight=1)

        # Obsidian风格的动态工具栏
        self.create_dynamic_toolbar()

        # 创建滚动区域以支持长内容
        self.main_scroll_frame = ctk.CTkScrollableFrame(
            self.middle_frame,
            fg_color="transparent"
        )
        self.main_scroll_frame.grid(row=1, column=0, sticky="nsew", padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD))
        self.main_scroll_frame.grid_columnconfigure(0, weight=1)
        self.middle_frame.grid_rowconfigure(1, weight=1)

        # 基本信息区 - 最重要的信息
        basic_info_frame = ctk.CTkFrame(
            self.main_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        basic_info_frame.grid(row=0, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        basic_info_frame.grid_columnconfigure(1, weight=1)

        # 区域标题
        title_label = ctk.CTkLabel(
            basic_info_frame,
            text="📝 基本信息",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        title_label.grid(row=0, column=0, columnspan=2, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        # 状态选择
        ctk.CTkLabel(
            basic_info_frame,
            text="状态:",
            anchor="w",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=1, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_SM, 0), sticky="w")

        self.status_combo = ctk.CTkComboBox(
            basic_info_frame,
            values=STATUS_LIST,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.status_combo.grid(row=1, column=1, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_SM, 0), sticky="ew")

        # 分类选择
        ctk.CTkLabel(
            basic_info_frame,
            text="分类:",
            anchor="w",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=2, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_SM, 0), sticky="w")

        self.category_combo = ctk.CTkComboBox(
            basic_info_frame,
            values=CATEGORY_LIST,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.category_combo.grid(row=2, column=1, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_SM, DesignSystem.SPACING_MD), sticky="ew")

        # 核心想法
        ctk.CTkLabel(
            basic_info_frame,
            text="核心想法:",
            anchor="w",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=2, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, 0), sticky="w")

        core_idea_frame = ctk.CTkFrame(basic_info_frame, fg_color="transparent")
        core_idea_frame.grid(row=2, column=1, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_MD), sticky="ew")
        core_idea_frame.grid_columnconfigure(0, weight=1)

        self.core_idea_entry = ctk.CTkEntry(
            core_idea_frame,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT,
            height=36
        )
        self.core_idea_entry.grid(row=0, column=0, sticky="ew")
        self.core_idea_entry.bind("<KeyRelease>", lambda e: self.schedule_auto_save())
        
        self.ai_title_button = ctk.CTkButton(
            core_idea_frame,
            text="✨ AI标题",
            width=90,
            command=self.start_generate_title_ai,
            fg_color=DesignSystem.ACCENT_PURPLE,
            hover_color=DesignSystem.ACCENT_PURPLE,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL, weight="bold"),
            height=36
        )
        self.ai_title_button.grid(row=0, column=1, padx=(DesignSystem.SPACING_SM, 0))

        # 标签区域
        tag_section_frame = ctk.CTkFrame(
            self.main_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        tag_section_frame.grid(row=1, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        tag_section_frame.grid_columnconfigure(0, weight=1)

        # 标签区域标题
        tag_title_label = ctk.CTkLabel(
            tag_section_frame,
            text="🏷️ 标签管理",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        tag_title_label.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        tag_area_frame = ctk.CTkFrame(tag_section_frame, fg_color="transparent")
        tag_area_frame.grid(row=1, column=0, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD), sticky="ew")
        tag_area_frame.grid_columnconfigure(0, weight=1)

        self.tag_selection_frame = ctk.CTkScrollableFrame(tag_area_frame, label_text="选择或创建标签", orientation="horizontal", height=60)
        self.tag_selection_frame.grid(row=0, column=0, sticky="ew")

        tag_creation_frame = ctk.CTkFrame(tag_area_frame, fg_color="transparent")
        tag_creation_frame.grid(row=0, column=1, padx=(5, 0))

        self.add_tag_entry = ctk.CTkEntry(tag_creation_frame, placeholder_text="+新标签", width=80)
        self.add_tag_entry.pack()
        self.add_tag_entry.bind("<Return>", self.add_new_tag_to_selection)

        # AI建议风格选择
        self.ai_style_combo = ctk.CTkComboBox(
            tag_creation_frame,
            values=["通用风格", "技术风格", "商业风格", "创意风格", "学术风格"],
            width=80,
            font=ctk.CTkFont(size=10)
        )
        self.ai_style_combo.pack(pady=(2, 0))
        self.ai_style_combo.set("通用风格")

        ai_button_frame = ctk.CTkFrame(tag_creation_frame, fg_color="transparent")
        ai_button_frame.pack(pady=(2, 0))

        self.ai_suggest_button = ctk.CTkButton(
            ai_button_frame,
            text="🤖 AI建议",
            command=self.start_suggest_tags_ai,
            fg_color="#FF69B4",
            hover_color="#FF1493",
            font=ctk.CTkFont(size=10, weight="bold"),
            width=60
        )
        self.ai_suggest_button.pack(side="left", padx=(0, 2))

        self.ai_regenerate_button = ctk.CTkButton(
            ai_button_frame,
            text="🔄",
            command=self.regenerate_tags_ai,
            fg_color="#9370DB",
            hover_color="#8A2BE2",
            font=ctk.CTkFont(size=10, weight="bold"),
            width=20
        )
        self.ai_regenerate_button.pack(side="left")

        # 详细内容区 - Notion风格的卡片设计
        content_frame = ctk.CTkFrame(
            self.main_scroll_frame,
            fg_color=DesignSystem.CARD_BG,
            corner_radius=DesignSystem.RADIUS_LG,
            border_width=1,
            border_color=DesignSystem.BORDER_LIGHT
        )
        content_frame.grid(row=2, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_XL))
        content_frame.grid_columnconfigure(0, weight=1)

        # 内容区域标题
        content_title_label = ctk.CTkLabel(
            content_frame,
            text="� 详细内容",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        content_title_label.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        # 背景/来源区域 - Notion风格的内嵌卡片
        context_section = ctk.CTkFrame(
            content_frame,
            fg_color=DesignSystem.SECONDARY_BG,
            corner_radius=DesignSystem.RADIUS_MD,
            border_width=0
        )
        context_section.grid(row=1, column=0, padx=DesignSystem.SPACING_XL, pady=(DesignSystem.SPACING_LG, DesignSystem.SPACING_MD), sticky="ew")
        context_section.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            context_section,
            text="💭 背景/来源",
            anchor="w",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=0, column=0, padx=DesignSystem.SPACING_SM, pady=(DesignSystem.SPACING_SM, DesignSystem.SPACING_XS), sticky="w")

        self.context_textbox = ctk.CTkTextbox(
            context_section,
            height=80,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.CARD_BG,
            border_color=DesignSystem.BORDER_LIGHT,
            border_width=1
        )
        self.context_textbox.grid(row=1, column=0, padx=DesignSystem.SPACING_SM, pady=(0, DesignSystem.SPACING_SM), sticky="ew")
        self.context_textbox.bind("<KeyRelease>", lambda e: self.schedule_auto_save())

        # 潜在应用区域 - Notion风格的内嵌卡片
        application_section = ctk.CTkFrame(
            content_frame,
            fg_color=DesignSystem.SECONDARY_BG,
            corner_radius=DesignSystem.RADIUS_MD,
            border_width=0
        )
        application_section.grid(row=2, column=0, padx=DesignSystem.SPACING_XL, pady=(0, DesignSystem.SPACING_MD), sticky="ew")
        application_section.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            application_section,
            text="🚀 潜在应用",
            anchor="w",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=0, column=0, padx=DesignSystem.SPACING_SM, pady=(DesignSystem.SPACING_SM, DesignSystem.SPACING_XS), sticky="w")

        self.application_textbox = ctk.CTkTextbox(
            application_section,
            height=80,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.CARD_BG,
            border_color=DesignSystem.BORDER_LIGHT,
            border_width=1
        )
        self.application_textbox.grid(row=1, column=0, padx=DesignSystem.SPACING_SM, pady=(0, DesignSystem.SPACING_SM), sticky="ew")
        self.application_textbox.bind("<KeyRelease>", lambda e: self.schedule_auto_save())

        # 相关问题区域 - Notion风格的内嵌卡片
        questions_section = ctk.CTkFrame(
            content_frame,
            fg_color=DesignSystem.SECONDARY_BG,
            corner_radius=DesignSystem.RADIUS_MD,
            border_width=0
        )
        questions_section.grid(row=3, column=0, padx=DesignSystem.SPACING_XL, pady=(0, DesignSystem.SPACING_XL), sticky="ew")
        questions_section.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            questions_section,
            text="❓ 相关问题",
            anchor="w",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=0, column=0, padx=DesignSystem.SPACING_SM, pady=(DesignSystem.SPACING_SM, DesignSystem.SPACING_XS), sticky="w")

        self.questions_textbox = ctk.CTkTextbox(
            questions_section,
            height=80,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.CARD_BG,
            border_color=DesignSystem.BORDER_LIGHT,
            border_width=1
        )
        self.questions_textbox.grid(row=1, column=0, padx=DesignSystem.SPACING_SM, pady=(0, DesignSystem.SPACING_SM), sticky="ew")
        self.questions_textbox.bind("<KeyRelease>", lambda e: self.schedule_auto_save())

        # 链接和操作区
        action_frame = ctk.CTkFrame(
            self.main_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        action_frame.grid(row=3, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        action_frame.grid_columnconfigure(0, weight=1)

        # 操作区域标题
        action_title_label = ctk.CTkLabel(
            action_frame,
            text="🔗 链接与操作",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        action_title_label.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        # 手动链接部分
        link_section = ctk.CTkFrame(action_frame, fg_color=DesignSystem.PRIMARY_BG, corner_radius=DesignSystem.SPACING_XS)
        link_section.grid(row=1, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_SM, DesignSystem.SPACING_SM), sticky="ew")
        link_section.grid_columnconfigure(0, weight=1)

        link_header_frame = ctk.CTkFrame(link_section, fg_color="transparent")
        link_header_frame.grid(row=0, column=0, padx=DesignSystem.SPACING_SM, pady=(DesignSystem.SPACING_SM, DesignSystem.SPACING_XS), sticky="ew")
        link_header_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            link_header_frame,
            text="🔗 手动链接",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        ).grid(row=0, column=0, sticky="w")
        self.add_link_button = ctk.CTkButton(
            link_header_frame,
            text="+ 添加链接",
            width=80,
            command=self.open_linker_window,
            fg_color="#4169E1",
            hover_color="#1E90FF",
            font=ctk.CTkFont(size=11)
        )
        self.add_link_button.grid(row=0, column=1, sticky="e")

        self.linked_items_container = ctk.CTkFrame(action_frame, fg_color="transparent")
        self.linked_items_container.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="nsew")

        # 操作按钮区
        bottom_button_frame = ctk.CTkFrame(self.middle_frame, fg_color="transparent")
        bottom_button_frame.grid(row=4, column=0, padx=15, pady=10, sticky="ew")
        bottom_button_frame.grid_columnconfigure(0, weight=1)

        self.ai_expand_button = ctk.CTkButton(
            bottom_button_frame,
            text="� AI 扩展想法",
            command=self.start_expand_idea_ai,
            fg_color="#20B2AA",
            hover_color="#008B8B",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.ai_expand_button.grid(row=0, column=0, sticky="w")

        self.preview_button = ctk.CTkButton(
            bottom_button_frame,
            text="📝 预览",
            command=self.open_markdown_preview,
            fg_color="#9932CC",
            hover_color="#8B008B",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.preview_button.grid(row=0, column=1, sticky="e", padx=5)

        self.save_button = ctk.CTkButton(
            bottom_button_frame,
            text="💾 保存想法",
            command=self.save_current_idea,
            fg_color="#32CD32",
            hover_color="#228B22",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.save_button.grid(row=0, column=2, sticky="e", padx=5)

        self.delete_button = ctk.CTkButton(
            bottom_button_frame,
            text="🗑️ 删除",
            fg_color="#DC143C",
            hover_color="#B22222",
            command=self.delete_current_idea,
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.delete_button.grid(row=0, column=3, sticky="e")

        # 添加状态栏
        self.status_label = ctk.CTkLabel(
            self.middle_frame,
            text="",
            font=ctk.CTkFont(size=11),
            text_color="gray"
        )
        self.status_label.grid(row=5, column=0, padx=15, pady=(5, 10), sticky="ew")

    def _create_right_frame(self):
        """创建右侧AI助手面板 - 应用清晰的功能分区和视觉层次"""
        self.right_frame = ctk.CTkFrame(
            self,
            corner_radius=DesignSystem.SPACING_SM,
            fg_color=DesignSystem.SECONDARY_BG
        )
        self.right_frame.grid(row=0, column=2, padx=DesignSystem.SPACING_MD, pady=DesignSystem.SPACING_MD, sticky="nsew")
        self.right_frame.grid_columnconfigure(0, weight=1)

        # 创建滚动区域
        self.right_scroll_frame = ctk.CTkScrollableFrame(
            self.right_frame,
            fg_color="transparent"
        )
        self.right_scroll_frame.grid(row=0, column=0, sticky="nsew", padx=DesignSystem.SPACING_MD, pady=DesignSystem.SPACING_MD)
        self.right_scroll_frame.grid_columnconfigure(0, weight=1)
        self.right_frame.grid_rowconfigure(0, weight=1)

        # 主标题区域
        title_frame = ctk.CTkFrame(self.right_scroll_frame, fg_color="transparent")
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))

        title_label = ctk.CTkLabel(
            title_frame,
            text="🤖 AI 创作助手",
            font=ctk.CTkFont(size=DesignSystem.FONT_LARGE, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        title_label.pack()

        # 智能分析区域 - 新增的核心功能
        analysis_section = ctk.CTkFrame(
            self.right_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        analysis_section.grid(row=1, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        analysis_section.grid_columnconfigure(0, weight=1)

        analysis_title = ctk.CTkLabel(
            analysis_section,
            text="🧠 智能分析",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        analysis_title.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        # 智能分析按钮
        self.smart_analysis_button = ctk.CTkButton(
            analysis_section,
            text="🧠 深度分析当前想法",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            fg_color=DesignSystem.ACCENT_PURPLE,
            hover_color=DesignSystem.ACCENT_PURPLE_LIGHT,
            height=40,
            command=self.start_smart_analysis
        )
        self.smart_analysis_button.grid(row=1, column=0, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_SM), sticky="ew")

        # AI洞察显示区域
        self.ai_insights_frame = ctk.CTkScrollableFrame(
            analysis_section,
            fg_color=DesignSystem.PRIMARY_BG,
            height=200,
            corner_radius=DesignSystem.SPACING_XS
        )
        self.ai_insights_frame.grid(row=2, column=0, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD), sticky="ew")

        # 默认提示
        insights_placeholder = ctk.CTkLabel(
            self.ai_insights_frame,
            text="💭 点击上方按钮开始AI深度分析\n\n将为您提供：\n• 核心观点提炼\n• 延展思考问题\n• 智能标签建议\n• 情感倾向分析",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            justify="center"
        )
        insights_placeholder.pack(expand=True, pady=DesignSystem.SPACING_LG)

        # 涌现洞察区域 - 新增的高级功能
        emergence_section = ctk.CTkFrame(
            self.right_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        emergence_section.grid(row=2, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        emergence_section.grid_columnconfigure(0, weight=1)

        emergence_title = ctk.CTkLabel(
            emergence_section,
            text="🌟 涌现洞察",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        emergence_title.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        # 洞察发现按钮
        self.emergence_button = ctk.CTkButton(
            emergence_section,
            text="🌟 发现思维模式",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            fg_color=DesignSystem.ACCENT_ORANGE,
            hover_color=DesignSystem.ACCENT_ORANGE_LIGHT,
            height=40,
            command=self.discover_emergent_insights
        )
        self.emergence_button.grid(row=1, column=0, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_SM), sticky="ew")

        # 洞察显示区域
        self.emergence_insights_frame = ctk.CTkScrollableFrame(
            emergence_section,
            fg_color=DesignSystem.PRIMARY_BG,
            height=150,
            corner_radius=DesignSystem.SPACING_XS
        )
        self.emergence_insights_frame.grid(row=2, column=0, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD), sticky="ew")

        # 默认提示
        emergence_placeholder = ctk.CTkLabel(
            self.emergence_insights_frame,
            text="🧠 点击上方按钮发现思维模式\n\n将为您揭示：\n• 想法聚类分析\n• 思维模式识别\n• 知识空白发现\n• 发展趋势洞察",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            justify="center"
        )
        emergence_placeholder.pack(expand=True, pady=DesignSystem.SPACING_LG)

        # AI生成结果区域
        result_section = ctk.CTkFrame(
            self.right_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        result_section.grid(row=1, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        result_section.grid_columnconfigure(0, weight=1)

        result_title = ctk.CTkLabel(
            result_section,
            text="✨ 生成结果",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        result_title.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        self.generation_result_frame = ctk.CTkScrollableFrame(
            result_section,
            fg_color=DesignSystem.PRIMARY_BG,
            height=150,
            corner_radius=DesignSystem.SPACING_XS
        )
        self.generation_result_frame.grid(row=1, column=0, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD), sticky="ew")

        placeholder_label = ctk.CTkLabel(
            self.generation_result_frame,
            text="💡 这里将显示AI生成的链接和推荐内容",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            justify="center"
        )
        placeholder_label.pack(expand=True, pady=DesignSystem.SPACING_LG)

        # 关键词碰撞区域
        collision_section = ctk.CTkFrame(
            self.right_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        collision_section.grid(row=2, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        collision_section.grid_columnconfigure((0, 1), weight=1)

        collision_title = ctk.CTkLabel(
            collision_section,
            text="⚡ 关键词碰撞",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        collision_title.grid(row=0, column=0, columnspan=2, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        self.keyword_combo_1 = ctk.CTkComboBox(
            collision_section,
            values=[" "],
            command=self.perform_collision,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.keyword_combo_1.grid(row=1, column=0, padx=(DesignSystem.SPACING_MD, DesignSystem.SPACING_XS), pady=(0, DesignSystem.SPACING_SM), sticky="ew")

        self.keyword_combo_2 = ctk.CTkComboBox(
            collision_section,
            values=[" "],
            command=self.perform_collision,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.keyword_combo_2.grid(row=1, column=1, padx=(DesignSystem.SPACING_XS, DesignSystem.SPACING_MD), pady=(0, DesignSystem.SPACING_SM), sticky="ew")

        self.random_button = ctk.CTkButton(
            collision_section,
            text="⚡ 随机灵感碰撞",
            height=40,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL, weight="bold"),
            command=self.perform_random_inspiration,
            fg_color=DesignSystem.ACCENT_ORANGE,
            hover_color=DesignSystem.ACCENT_ORANGE,
            corner_radius=DesignSystem.SPACING_SM
        )
        self.random_button.grid(row=2, column=0, columnspan=2, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD), sticky="ew")

        # 智能推荐区域
        recommendation_section = ctk.CTkFrame(
            self.right_scroll_frame,
            fg_color=DesignSystem.TERTIARY_BG,
            corner_radius=DesignSystem.SPACING_SM
        )
        recommendation_section.grid(row=3, column=0, sticky="ew")
        recommendation_section.grid_columnconfigure(0, weight=1)

        recommendation_title = ctk.CTkLabel(
            recommendation_section,
            text="🧠 智能推荐",
            font=ctk.CTkFont(size=DesignSystem.FONT_MEDIUM, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        recommendation_title.grid(row=0, column=0, padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM), sticky="w")

        self.smart_recommendations_frame = ctk.CTkScrollableFrame(
            recommendation_section,
            fg_color=DesignSystem.PRIMARY_BG,
            height=200,
            corner_radius=DesignSystem.SPACING_XS
        )
        self.smart_recommendations_frame.grid(row=1, column=0, padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD), sticky="ew")

    def open_settings_window(self):
        SettingsWindow(self)

    def open_export_dialog(self):
        """打开导出对话框"""
        ExportDialog(self)

    def get_full_text_content(self):
        """获取当前想法的完整文本内容"""
        content_parts = [
            self.core_idea_entry.get(),
            self.context_textbox.get("1.0", "end-1c"),
            self.application_textbox.get("1.0", "end-1c"),
            self.questions_textbox.get("1.0", "end-1c")
        ]
        return " ".join([part.strip() for part in content_parts if part.strip()])

    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        # Ctrl+N: 新建想法
        self.bind("<Control-n>", lambda e: self.clear_form())

        # Ctrl+S: 保存想法
        self.bind("<Control-s>", lambda e: self.save_current_idea())

        # Ctrl+E: 导出
        self.bind("<Control-e>", lambda e: self.open_export_dialog())

        # Ctrl+G: 图谱视图
        self.bind("<Control-g>", lambda e: self.open_graph_view())

        # Ctrl+T: 标签管理
        self.bind("<Control-t>", lambda e: self.open_tag_manager())

        # Ctrl+R: 回收站
        self.bind("<Control-r>", lambda e: self.open_recycle_bin_window())

        # Ctrl+P: 预览
        self.bind("<Control-p>", lambda e: self.open_markdown_preview())

        # F1: 显示快捷键帮助
        self.bind("<F1>", lambda e: self.show_shortcuts_help())

        # F2: 新手教程
        self.bind("<F2>", lambda e: self.open_tutorial_manually())

        # Delete: 删除当前想法
        self.bind("<Delete>", lambda e: self.delete_current_idea() if self.current_idea_id else None)

        # === Obsidian风格的动态布局快捷键 ===
        # Ctrl+\: 切换左面板
        self.bind("<Control-backslash>", lambda e: self.toggle_left_panel())

        # Ctrl+Shift+\: 切换右面板
        self.bind("<Control-Shift-backslash>", lambda e: self.toggle_right_panel())

        # F11: 专注模式
        self.bind("<F11>", lambda e: self.toggle_focus_mode())

        # Ctrl+1/2/3: 切换视图模式
        self.bind("<Control-Key-1>", lambda e: self.switch_view_mode("edit"))
        self.bind("<Control-Key-2>", lambda e: self.switch_view_mode("preview"))
        self.bind("<Control-Key-3>", lambda e: self.switch_view_mode("graph"))

        # 设置焦点，使快捷键生效
        self.focus_set()

        # 启动动态布局监控
        self.start_dynamic_layout_monitoring()

    def start_dynamic_layout_monitoring(self):
        """启动Obsidian风格的动态布局监控"""
        self.bind("<Motion>", self.on_mouse_motion)
        self.bind("<Button-1>", self.on_user_interaction)
        self.bind("<Key>", self.on_user_interaction)

        # 定期检查是否需要自动调整布局
        self.after(1000, self.check_auto_layout_adjustment)

    def on_mouse_motion(self, event):
        """鼠标移动事件 - 用于自动显示/隐藏面板"""
        import time
        self.layout_state["last_interaction_time"] = time.time()

        # 检查是否需要自动显示面板
        if self.layout_state["auto_hide_panels"]:
            window_width = self.winfo_width()

            # 鼠标靠近左边缘时显示左面板
            if event.x < 50 and not self.layout_state["left_panel_visible"]:
                self.toggle_left_panel(True)

            # 鼠标靠近右边缘时显示右面板
            if event.x > window_width - 50 and not self.layout_state["right_panel_visible"]:
                self.toggle_right_panel(True)

    def on_user_interaction(self, event):
        """用户交互事件"""
        import time
        self.layout_state["last_interaction_time"] = time.time()

    def check_auto_layout_adjustment(self):
        """检查是否需要自动调整布局"""
        import time
        current_time = time.time()

        # 如果启用了自动隐藏且用户长时间无操作，隐藏面板
        if (self.layout_state["auto_hide_panels"] and
            current_time - self.layout_state["last_interaction_time"] > 10):

            if self.layout_state["left_panel_visible"]:
                self.toggle_left_panel(False)
            if self.layout_state["right_panel_visible"]:
                self.toggle_right_panel(False)

        # 继续监控
        self.after(1000, self.check_auto_layout_adjustment)

    def toggle_left_panel(self, show=None):
        """切换左面板显示状态"""
        if show is None:
            show = not self.layout_state["left_panel_visible"]

        self.layout_state["left_panel_visible"] = show

        if show:
            self.left_frame.grid()
            self.grid_columnconfigure(0, weight=0, minsize=self.layout_state["left_panel_width"])
        else:
            self.left_frame.grid_remove()
            self.grid_columnconfigure(0, weight=0, minsize=0)

        # 平滑动画效果
        self.animate_layout_change()

    def toggle_right_panel(self, show=None):
        """切换右面板显示状态"""
        if show is None:
            show = not self.layout_state["right_panel_visible"]

        self.layout_state["right_panel_visible"] = show

        if show:
            self.right_frame.grid()
            self.grid_columnconfigure(2, weight=0, minsize=self.layout_state["right_panel_width"])
        else:
            self.right_frame.grid_remove()
            self.grid_columnconfigure(2, weight=0, minsize=0)

        # 平滑动画效果
        self.animate_layout_change()

    def animate_layout_change(self):
        """布局变化的平滑动画效果"""
        # 简单的布局更新，可以后续添加更复杂的动画
        self.update_idletasks()

    def toggle_focus_mode(self):
        """切换专注模式 - 隐藏所有面板，只显示编辑区"""
        self.layout_state["focus_mode"] = not self.layout_state["focus_mode"]

        if self.layout_state["focus_mode"]:
            # 进入专注模式
            self.layout_state["left_panel_visible"] = False
            self.layout_state["right_panel_visible"] = False
            self.left_frame.grid_remove()
            self.right_frame.grid_remove()
            self.grid_columnconfigure(0, weight=0, minsize=0)
            self.grid_columnconfigure(2, weight=0, minsize=0)
        else:
            # 退出专注模式
            self.layout_state["left_panel_visible"] = True
            self.layout_state["right_panel_visible"] = True
            self.left_frame.grid()
            self.right_frame.grid()
            self.grid_columnconfigure(0, weight=0, minsize=self.layout_state["left_panel_width"])
            self.grid_columnconfigure(2, weight=0, minsize=self.layout_state["right_panel_width"])

        self.animate_layout_change()

    def create_dynamic_toolbar(self):
        """创建Obsidian风格的动态工具栏"""
        self.toolbar_frame = ctk.CTkFrame(
            self.middle_frame,
            fg_color=DesignSystem.SECONDARY_BG,
            height=40,
            corner_radius=DesignSystem.RADIUS_MD
        )
        self.toolbar_frame.grid(row=0, column=0, sticky="ew", padx=DesignSystem.SPACING_MD, pady=(DesignSystem.SPACING_MD, DesignSystem.SPACING_SM))
        self.toolbar_frame.grid_propagate(False)

        # 左侧：视图切换按钮
        view_frame = ctk.CTkFrame(self.toolbar_frame, fg_color="transparent")
        view_frame.pack(side="left", padx=DesignSystem.SPACING_SM, pady=DesignSystem.SPACING_XS)

        # 编辑模式按钮
        self.edit_mode_btn = ctk.CTkButton(
            view_frame,
            text="✏️ 编辑",
            width=60,
            height=28,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.ACCENT_BLUE if self.layout_state["current_view_mode"] == "edit" else "transparent",
            hover_color=DesignSystem.ACCENT_BLUE_LIGHT,
            command=lambda: self.switch_view_mode("edit")
        )
        self.edit_mode_btn.pack(side="left", padx=2)

        # 预览模式按钮
        self.preview_mode_btn = ctk.CTkButton(
            view_frame,
            text="👁️ 预览",
            width=60,
            height=28,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.ACCENT_GREEN if self.layout_state["current_view_mode"] == "preview" else "transparent",
            hover_color=DesignSystem.ACCENT_GREEN_LIGHT,
            command=lambda: self.switch_view_mode("preview")
        )
        self.preview_mode_btn.pack(side="left", padx=2)

        # 图谱模式按钮
        self.graph_mode_btn = ctk.CTkButton(
            view_frame,
            text="🌐 图谱",
            width=60,
            height=28,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.ACCENT_PURPLE if self.layout_state["current_view_mode"] == "graph" else "transparent",
            hover_color=DesignSystem.ACCENT_PURPLE_LIGHT,
            command=lambda: self.switch_view_mode("graph")
        )
        self.graph_mode_btn.pack(side="left", padx=2)

        # 结构化模式按钮
        self.structured_mode_btn = ctk.CTkButton(
            view_frame,
            text="🏗️ 结构",
            width=60,
            height=28,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.ACCENT_ORANGE if self.layout_state["current_view_mode"] == "structured" else "transparent",
            hover_color=DesignSystem.ACCENT_ORANGE_LIGHT,
            command=lambda: self.switch_view_mode("structured")
        )
        self.structured_mode_btn.pack(side="left", padx=2)

        # 中间：当前想法信息
        info_frame = ctk.CTkFrame(self.toolbar_frame, fg_color="transparent")
        info_frame.pack(side="left", expand=True, fill="x", padx=DesignSystem.SPACING_MD)

        self.current_idea_label = ctk.CTkLabel(
            info_frame,
            text="💡 新建想法",
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL, weight="bold"),
            text_color=DesignSystem.TEXT_SECONDARY
        )
        self.current_idea_label.pack(side="left")

        # 右侧：面板控制按钮
        control_frame = ctk.CTkFrame(self.toolbar_frame, fg_color="transparent")
        control_frame.pack(side="right", padx=DesignSystem.SPACING_SM, pady=DesignSystem.SPACING_XS)

        # 左面板切换
        self.left_panel_btn = ctk.CTkButton(
            control_frame,
            text="📋",
            width=28,
            height=28,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.ACCENT_GRAY if self.layout_state["left_panel_visible"] else "transparent",
            hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
            command=self.toggle_left_panel
        )
        self.left_panel_btn.pack(side="left", padx=2)

        # 专注模式切换
        self.focus_mode_btn = ctk.CTkButton(
            control_frame,
            text="🎯",
            width=28,
            height=28,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.ACCENT_ORANGE if self.layout_state["focus_mode"] else "transparent",
            hover_color=DesignSystem.ACCENT_ORANGE_LIGHT,
            command=self.toggle_focus_mode
        )
        self.focus_mode_btn.pack(side="left", padx=2)

        # 右面板切换
        self.right_panel_btn = ctk.CTkButton(
            control_frame,
            text="🤖",
            width=28,
            height=28,
            font=ctk.CTkFont(size=DesignSystem.FONT_SMALL),
            fg_color=DesignSystem.ACCENT_GRAY if self.layout_state["right_panel_visible"] else "transparent",
            hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
            command=self.toggle_right_panel
        )
        self.right_panel_btn.pack(side="left", padx=2)

    def switch_view_mode(self, mode):
        """切换视图模式"""
        self.layout_state["current_view_mode"] = mode

        # 更新按钮状态
        self.edit_mode_btn.configure(
            fg_color=DesignSystem.ACCENT_BLUE if mode == "edit" else "transparent"
        )
        self.preview_mode_btn.configure(
            fg_color=DesignSystem.ACCENT_GREEN if mode == "preview" else "transparent"
        )
        self.graph_mode_btn.configure(
            fg_color=DesignSystem.ACCENT_PURPLE if mode == "graph" else "transparent"
        )
        self.structured_mode_btn.configure(
            fg_color=DesignSystem.ACCENT_ORANGE if mode == "structured" else "transparent"
        )

        # 根据模式调整界面
        if mode == "edit":
            self.show_edit_view()
        elif mode == "preview":
            self.show_preview_view()
        elif mode == "graph":
            self.show_graph_view()
        elif mode == "structured":
            self.show_structured_view()

    def show_edit_view(self):
        """显示编辑视图"""
        self.main_scroll_frame.grid(row=1, column=0, sticky="nsew", padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD))
        # 隐藏结构化视图
        if hasattr(self, 'structured_frame'):
            self.structured_frame.grid_remove()

    def show_preview_view(self):
        """显示预览视图"""
        # TODO: 实现预览视图
        self.show_status_message("📖 预览模式开发中...", "info", 2000)

    def show_graph_view(self):
        """显示图谱视图"""
        # TODO: 集成图谱视图到主界面
        self.open_graph_view()

    def show_structured_view(self):
        """显示结构化视图"""
        # 隐藏普通编辑视图
        self.main_scroll_frame.grid_remove()

        # 创建或显示结构化视图
        if not hasattr(self, 'structured_frame'):
            self.create_structured_view()
        else:
            self.structured_frame.grid(row=1, column=0, sticky="nsew", padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD))

        # 如果有当前想法，加载其结构化数据
        if self.current_idea_id:
            self.load_structured_data()

    def navigate_back(self):
        """后退到上一页"""
        if self.page_manager.can_go_back():
            previous_page = self.page_manager.go_back()
            self.render_current_page()
            self.update_navigation_ui()

    def navigate_to_page(self, page_type, page_data=None):
        """导航到指定页面"""
        self.page_manager.navigate_to(page_type, page_data)
        self.render_current_page()
        self.update_navigation_ui()

    def render_current_page(self):
        """渲染当前页面"""
        current_page = self.page_manager.current_page
        if not current_page:
            self.show_idea_list_page()
            return

        page_type = current_page["type"]
        page_data = current_page.get("data")

        if page_type == "idea_list":
            self.show_idea_list_page()
        elif page_type == "category_list":
            self.show_category_page(page_data)
        elif page_type == "tag_view":
            self.show_tag_page(page_data)
        elif page_type == "idea_detail":
            self.show_idea_detail_page(page_data)

    def update_navigation_ui(self):
        """更新导航界面"""
        # 更新后退按钮状态
        if self.page_manager.can_go_back():
            self.back_button.configure(state="normal")
        else:
            self.back_button.configure(state="disabled")

        # 更新面包屑导航
        self.update_breadcrumb()

    def update_breadcrumb(self):
        """更新面包屑导航"""
        # 清除现有面包屑
        for widget in self.breadcrumb_frame.winfo_children():
            widget.destroy()

        breadcrumb = self.page_manager.get_breadcrumb()
        if not breadcrumb:
            breadcrumb = ["💡 想法库"]

        for i, crumb in enumerate(breadcrumb):
            if i > 0:
                # 添加分隔符
                separator = ctk.CTkLabel(
                    self.breadcrumb_frame,
                    text="›",
                    font=ctk.CTkFont(size=12),
                    text_color=DesignSystem.TEXT_SECONDARY
                )
                separator.pack(side="left", padx=2)

            # 添加面包屑项
            crumb_button = ctk.CTkButton(
                self.breadcrumb_frame,
                text=crumb,
                font=ctk.CTkFont(size=12),
                fg_color="transparent",
                hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
                height=24,
                command=lambda idx=i: self.navigate_to_breadcrumb_level(idx)
            )
            crumb_button.pack(side="left", padx=1)

    def navigate_to_breadcrumb_level(self, level):
        """导航到面包屑的指定层级"""
        # 回退到指定层级
        while len(self.page_manager.page_stack) > level:
            self.page_manager.page_stack.pop()

        if level < len(self.page_manager.page_stack):
            self.page_manager.current_page = self.page_manager.page_stack[level]
            self.page_manager.page_stack = self.page_manager.page_stack[:level]

        self.render_current_page()
        self.update_navigation_ui()

    def show_idea_list_page(self):
        """显示想法列表主页面"""
        # 清除现有内容
        for widget in self.idea_list_frame.winfo_children():
            widget.destroy()

        # 创建分类导航
        categories = self.get_idea_categories()
        if categories:
            category_frame = ctk.CTkFrame(self.idea_list_frame, fg_color="transparent")
            category_frame.pack(fill="x", padx=10, pady=(10, 5))

            ctk.CTkLabel(
                category_frame,
                text="📁 分类浏览",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=DesignSystem.TEXT_PRIMARY
            ).pack(anchor="w")

            for category in categories:
                cat_button = ctk.CTkButton(
                    category_frame,
                    text=f"📁 {category['name']} ({category['count']})",
                    fg_color="transparent",
                    hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
                    anchor="w",
                    command=lambda cat=category: self.navigate_to_page("category_list", cat)
                )
                cat_button.pack(fill="x", pady=1)

        # 创建标签云
        tags = self.get_popular_tags()
        if tags:
            tag_frame = ctk.CTkFrame(self.idea_list_frame, fg_color="transparent")
            tag_frame.pack(fill="x", padx=10, pady=5)

            ctk.CTkLabel(
                tag_frame,
                text="🏷️ 热门标签",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=DesignSystem.TEXT_PRIMARY
            ).pack(anchor="w")

            tag_container = ctk.CTkFrame(tag_frame, fg_color="transparent")
            tag_container.pack(fill="x", pady=5)

            for tag in tags[:10]:  # 显示前10个热门标签
                tag_button = ctk.CTkButton(
                    tag_container,
                    text=f"#{tag['name']} ({tag['count']})",
                    fg_color=DesignSystem.ACCENT_BLUE,
                    hover_color=DesignSystem.ACCENT_BLUE_LIGHT,
                    height=24,
                    font=ctk.CTkFont(size=11),
                    command=lambda t=tag: self.navigate_to_page("tag_view", t)
                )
                tag_button.pack(side="left", padx=2, pady=2)

        # 显示最近的想法
        recent_ideas = [idea for idea in self.ideas if not idea.get('is_deleted')][:10]
        if recent_ideas:
            recent_frame = ctk.CTkFrame(self.idea_list_frame, fg_color="transparent")
            recent_frame.pack(fill="x", padx=10, pady=5)

            ctk.CTkLabel(
                recent_frame,
                text="⏰ 最近想法",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=DesignSystem.TEXT_PRIMARY
            ).pack(anchor="w")

            for idea in recent_ideas:
                idea_button = ctk.CTkButton(
                    recent_frame,
                    text=f"💡 {idea.get('core_idea', '无标题')[:40]}...",
                    fg_color="transparent",
                    hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
                    anchor="w",
                    command=lambda i=idea: self.display_idea(i['id'])
                )
                idea_button.pack(fill="x", pady=1)

    def get_idea_categories(self):
        """获取想法分类统计"""
        categories = {}
        for idea in self.ideas:
            if idea.get('is_deleted'):
                continue
            category = idea.get('category', '未分类')
            if category not in categories:
                categories[category] = 0
            categories[category] += 1

        return [{"name": name, "count": count} for name, count in sorted(categories.items(), key=lambda x: x[1], reverse=True)]

    def get_popular_tags(self):
        """获取热门标签统计"""
        tags = {}
        for idea in self.ideas:
            if idea.get('is_deleted'):
                continue
            for tag in idea.get('keywords', []):
                if tag not in tags:
                    tags[tag] = 0
                tags[tag] += 1

        return [{"name": name, "count": count} for name, count in sorted(tags.items(), key=lambda x: x[1], reverse=True)]

    def show_category_page(self, category_data):
        """显示分类页面"""
        # 清除现有内容
        for widget in self.idea_list_frame.winfo_children():
            widget.destroy()

        category_name = category_data["name"]

        # 页面标题
        title_frame = ctk.CTkFrame(self.idea_list_frame, fg_color="transparent")
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame,
            text=f"📁 {category_name}",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        ).pack(anchor="w")

        # 显示该分类下的想法
        category_ideas = [idea for idea in self.ideas
                         if not idea.get('is_deleted') and idea.get('category', '未分类') == category_name]

        if category_ideas:
            for idea in category_ideas:
                idea_frame = ctk.CTkFrame(self.idea_list_frame, fg_color=DesignSystem.CARD_BG)
                idea_frame.pack(fill="x", padx=10, pady=2)

                idea_button = ctk.CTkButton(
                    idea_frame,
                    text=f"💡 {idea.get('core_idea', '无标题')}",
                    fg_color="transparent",
                    hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
                    anchor="w",
                    command=lambda i=idea: self.navigate_to_page("idea_detail", {"id": i['id'], "title": i.get('core_idea', '无标题')})
                )
                idea_button.pack(fill="x", padx=5, pady=5)

                # 显示标签
                if idea.get('keywords'):
                    tag_frame = ctk.CTkFrame(idea_frame, fg_color="transparent")
                    tag_frame.pack(fill="x", padx=5, pady=(0, 5))

                    for tag in idea['keywords'][:5]:  # 最多显示5个标签
                        tag_label = ctk.CTkLabel(
                            tag_frame,
                            text=f"#{tag}",
                            font=ctk.CTkFont(size=10),
                            text_color=DesignSystem.ACCENT_BLUE
                        )
                        tag_label.pack(side="left", padx=2)
        else:
            ctk.CTkLabel(
                self.idea_list_frame,
                text="该分类下暂无想法",
                font=ctk.CTkFont(size=14),
                text_color=DesignSystem.TEXT_SECONDARY
            ).pack(expand=True)

    def show_tag_page(self, tag_data):
        """显示标签页面"""
        # 清除现有内容
        for widget in self.idea_list_frame.winfo_children():
            widget.destroy()

        tag_name = tag_data["name"]

        # 页面标题
        title_frame = ctk.CTkFrame(self.idea_list_frame, fg_color="transparent")
        title_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(
            title_frame,
            text=f"🏷️ #{tag_name}",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        ).pack(anchor="w")

        # 显示包含该标签的想法
        tag_ideas = [idea for idea in self.ideas
                    if not idea.get('is_deleted') and tag_name in idea.get('keywords', [])]

        if tag_ideas:
            for idea in tag_ideas:
                idea_frame = ctk.CTkFrame(self.idea_list_frame, fg_color=DesignSystem.CARD_BG)
                idea_frame.pack(fill="x", padx=10, pady=2)

                idea_button = ctk.CTkButton(
                    idea_frame,
                    text=f"💡 {idea.get('core_idea', '无标题')}",
                    fg_color="transparent",
                    hover_color=DesignSystem.ACCENT_GRAY_LIGHT,
                    anchor="w",
                    command=lambda i=idea: self.navigate_to_page("idea_detail", {"id": i['id'], "title": i.get('core_idea', '无标题')})
                )
                idea_button.pack(fill="x", padx=5, pady=5)
        else:
            ctk.CTkLabel(
                self.idea_list_frame,
                text="该标签下暂无想法",
                font=ctk.CTkFont(size=14),
                text_color=DesignSystem.TEXT_SECONDARY
            ).pack(expand=True)

    def show_idea_detail_page(self, idea_data):
        """显示想法详情页面"""
        idea_id = idea_data["id"]
        self.display_idea(idea_id)

    def create_structured_view(self):
        """创建结构化编辑视图，类似AI角色设定界面"""
        self.structured_frame = ctk.CTkScrollableFrame(
            self.middle_frame,
            fg_color="transparent"
        )
        self.structured_frame.grid(row=1, column=0, sticky="nsew", padx=DesignSystem.SPACING_MD, pady=(0, DesignSystem.SPACING_MD))
        self.structured_frame.grid_columnconfigure(0, weight=1)

        # 标题区域
        title_frame = ctk.CTkFrame(
            self.structured_frame,
            fg_color=DesignSystem.CARD_BG,
            corner_radius=DesignSystem.RADIUS_MD
        )
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_LG))
        title_frame.grid_columnconfigure(1, weight=1)

        # 想法标题
        self.structured_title_label = ctk.CTkLabel(
            title_frame,
            text="马斯克提示词",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        self.structured_title_label.grid(row=0, column=0, columnspan=2, padx=DesignSystem.SPACING_LG, pady=DesignSystem.SPACING_LG, sticky="w")

        # Role 标签
        role_label = ctk.CTkLabel(
            title_frame,
            text="Role: 第一性原理Mentor",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=DesignSystem.ACCENT_BLUE
        )
        role_label.grid(row=1, column=0, columnspan=2, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_LG), sticky="w")

        # Profile 区域
        self.create_structured_section("Profile", 1)

        # Background 区域
        self.create_structured_section("Background", 2)

        # Goals 区域
        self.create_structured_section("Goals", 3)

        # Constraints 区域
        self.create_structured_section("Constraints", 4)

    def create_structured_section(self, section_name, row_index):
        """创建结构化区域"""
        section_frame = ctk.CTkFrame(
            self.structured_frame,
            fg_color=DesignSystem.CARD_BG,
            corner_radius=DesignSystem.RADIUS_MD
        )
        section_frame.grid(row=row_index, column=0, sticky="ew", pady=(0, DesignSystem.SPACING_MD))
        section_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        title_label = ctk.CTkLabel(
            section_frame,
            text=f"{section_name}:",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY,
            anchor="w"
        )
        title_label.grid(row=0, column=0, padx=DesignSystem.SPACING_LG, pady=(DesignSystem.SPACING_LG, DesignSystem.SPACING_SM), sticky="w")

        # 内容区域
        if section_name == "Profile":
            self.create_profile_content(section_frame)
        elif section_name == "Background":
            self.create_background_content(section_frame)
        elif section_name == "Goals":
            self.create_goals_content(section_frame)
        elif section_name == "Constraints":
            self.create_constraints_content(section_frame)

    def create_profile_content(self, parent):
        """创建Profile内容"""
        # 作者信息
        author_label = ctk.CTkLabel(
            parent,
            text="• author: 作者",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            anchor="w"
        )
        author_label.grid(row=1, column=0, padx=(DesignSystem.SPACING_LG + 20, DesignSystem.SPACING_LG), pady=2, sticky="w")

        # 版本信息
        version_label = ctk.CTkLabel(
            parent,
            text="• version: 1.0",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            anchor="w"
        )
        version_label.grid(row=2, column=0, padx=(DesignSystem.SPACING_LG + 20, DesignSystem.SPACING_LG), pady=2, sticky="w")

        # 语言信息
        language_label = ctk.CTkLabel(
            parent,
            text="• language: 中文",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            anchor="w"
        )
        language_label.grid(row=3, column=0, padx=(DesignSystem.SPACING_LG + 20, DesignSystem.SPACING_LG), pady=2, sticky="w")

        # 描述信息
        self.profile_desc_text = ctk.CTkTextbox(
            parent,
            height=60,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.profile_desc_text.grid(row=4, column=0, padx=(DesignSystem.SPACING_LG + 20, DesignSystem.SPACING_LG), pady=(DesignSystem.SPACING_SM, DesignSystem.SPACING_LG), sticky="ew")

    def create_background_content(self, parent):
        """创建Background内容"""
        self.background_text = ctk.CTkTextbox(
            parent,
            height=120,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.background_text.grid(row=1, column=0, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_LG), sticky="ew")

    def create_goals_content(self, parent):
        """创建Goals内容"""
        self.goals_text = ctk.CTkTextbox(
            parent,
            height=100,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.goals_text.grid(row=1, column=0, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_LG), sticky="ew")

    def create_constraints_content(self, parent):
        """创建Constraints内容"""
        self.constraints_text = ctk.CTkTextbox(
            parent,
            height=100,
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            fg_color=DesignSystem.PRIMARY_BG,
            border_color=DesignSystem.BORDER_LIGHT
        )
        self.constraints_text.grid(row=1, column=0, padx=DesignSystem.SPACING_LG, pady=(0, DesignSystem.SPACING_LG), sticky="ew")

    def load_structured_data(self):
        """加载当前想法的结构化数据"""
        if not self.current_idea_id:
            return

        idea = next((i for i in self.ideas if i["id"] == self.current_idea_id), None)
        if not idea:
            return

        # 更新标题
        self.structured_title_label.configure(text=idea.get("core_idea", "未命名想法"))

        # 加载各区域内容
        if hasattr(self, 'profile_desc_text'):
            profile = idea.get("profile", {})
            self.profile_desc_text.delete("1.0", "end")
            self.profile_desc_text.insert("1.0", profile.get("description", ""))

        if hasattr(self, 'background_text'):
            self.background_text.delete("1.0", "end")
            self.background_text.insert("1.0", idea.get("background", ""))

        if hasattr(self, 'goals_text'):
            goals = idea.get("goals", [])
            goals_text = "\n".join([f"• {goal}" for goal in goals]) if goals else ""
            self.goals_text.delete("1.0", "end")
            self.goals_text.insert("1.0", goals_text)

        if hasattr(self, 'constraints_text'):
            constraints = idea.get("constraints", [])
            constraints_text = "\n".join([f"• {constraint}" for constraint in constraints]) if constraints else ""
            self.constraints_text.delete("1.0", "end")
            self.constraints_text.insert("1.0", constraints_text)

    def update_toolbar_info(self, idea_title=None):
        """更新工具栏中的当前想法信息"""
        if idea_title:
            self.current_idea_label.configure(text=f"💡 {idea_title[:30]}...")
        else:
            self.current_idea_label.configure(text="💡 新建想法")

        # 更新面板按钮状态
        self.left_panel_btn.configure(
            fg_color=DesignSystem.ACCENT_GRAY if self.layout_state["left_panel_visible"] else "transparent"
        )
        self.right_panel_btn.configure(
            fg_color=DesignSystem.ACCENT_GRAY if self.layout_state["right_panel_visible"] else "transparent"
        )
        self.focus_mode_btn.configure(
            fg_color=DesignSystem.ACCENT_ORANGE if self.layout_state["focus_mode"] else "transparent"
        )

    def check_tutorial_needed(self):
        """检查是否需要显示新手教程"""
        # 检查是否是首次使用（没有想法数据或配置文件）
        is_first_time = (
            len(self.ideas) == 0 and
            not os.path.exists(CONFIG_FILE) and
            not self.tutorial_completed
        )

        if is_first_time:
            self.show_tutorial()

    def show_tutorial(self):
        """显示新手教程"""
        TutorialWindow(self)

    def open_tutorial_manually(self):
        """手动打开教程（从菜单或按钮）"""
        TutorialWindow(self)

    def filter_ideas_by_category(self, selected_category=None):
        """根据分类筛选想法"""
        if selected_category is None:
            selected_category = self.category_filter.get()

        if selected_category == "全部":
            # 显示所有想法
            filtered_ideas = [idea for idea in self.ideas if not idea.get('is_deleted', False)]
        else:
            # 根据分类筛选
            filtered_ideas = [
                idea for idea in self.ideas
                if not idea.get('is_deleted', False) and idea.get('category', DEFAULT_CATEGORY) == selected_category
            ]

        self.refresh_idea_list(filtered_ideas)

    def show_shortcuts_help(self):
        """显示快捷键帮助"""
        help_window = ctk.CTkToplevel(self)
        help_window.title("⌨️ 快捷键帮助")
        help_window.geometry("500x600")
        help_window.transient(self)
        help_window.grab_set()

        # 标题
        title_label = ctk.CTkLabel(help_window, text="键盘快捷键", font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=20)

        # 快捷键列表
        shortcuts_frame = ctk.CTkScrollableFrame(help_window)
        shortcuts_frame.pack(fill="both", expand=True, padx=20, pady=10)

        shortcuts = [
            ("Ctrl + N", "新建想法"),
            ("Ctrl + S", "保存当前想法"),
            ("Ctrl + E", "导出想法"),
            ("Ctrl + G", "打开图谱视图"),
            ("Ctrl + T", "标签管理器"),
            ("Ctrl + R", "打开回收站"),
            ("Ctrl + P", "预览当前想法"),
            ("Delete", "删除当前想法"),
            ("F1", "显示此帮助"),
        ]

        for key, description in shortcuts:
            shortcut_frame = ctk.CTkFrame(shortcuts_frame, fg_color="transparent")
            shortcut_frame.pack(fill="x", pady=5)

            key_label = ctk.CTkLabel(shortcut_frame, text=key, font=ctk.CTkFont(weight="bold", family="Consolas"))
            key_label.pack(side="left", padx=(10, 20))

            desc_label = ctk.CTkLabel(shortcut_frame, text=description)
            desc_label.pack(side="left")

        # 关闭按钮
        close_button = ctk.CTkButton(help_window, text="关闭", command=help_window.destroy)
        close_button.pack(pady=20)

    def show_status_message(self, message, color="gray", duration=3000):
        """显示状态消息"""
        if self.status_label:
            self.status_label.configure(text=message, text_color=color)
            # 设置定时器清除消息
            self.after(duration, lambda: self.status_label.configure(text="") if self.status_label else None)

    def get_current_content_hash(self):
        """获取当前内容的哈希值，用于检测变化"""
        import hashlib
        content = f"{self.core_idea_entry.get()}{self.context_textbox.get('1.0', 'end-1c')}{self.application_textbox.get('1.0', 'end-1c')}{self.questions_textbox.get('1.0', 'end-1c')}"
        return hashlib.md5(content.encode()).hexdigest()

    def schedule_auto_save(self):
        """安排自动保存"""
        if self.auto_save_timer:
            self.after_cancel(self.auto_save_timer)

        current_hash = self.get_current_content_hash()
        if current_hash != self.last_content_hash and self.core_idea_entry.get().strip():
            self.auto_save_timer = self.after(5000, self.auto_save)  # 5秒后自动保存

    def auto_save(self):
        """自动保存功能"""
        if self.core_idea_entry.get().strip():
            current_hash = self.get_current_content_hash()
            if current_hash != self.last_content_hash:
                self.save_current_idea()
                self.last_content_hash = current_hash
                self.show_status_message("💾 自动保存完成", "blue", 2000)

    def open_markdown_preview(self):
        """打开Markdown预览窗口"""
        if not self.core_idea_entry.get().strip():
            self.show_status_message("❌ 请先输入内容", "red")
            return

        # 收集所有内容
        title = self.core_idea_entry.get()
        content_parts = []

        # 添加状态
        status = self.status_combo.get()
        content_parts.append(f"**状态**: {status}\n")

        # 添加标签
        tags = [v.get() for v in self.tag_checkboxes.values() if v.get()]
        if tags:
            content_parts.append(f"**标签**: {', '.join(tags)}\n")

        # 添加各个部分
        context = self.context_textbox.get("1.0", "end-1c").strip()
        if context:
            content_parts.append(f"## 背景/来源\n{context}\n")

        application = self.application_textbox.get("1.0", "end-1c").strip()
        if application:
            content_parts.append(f"## 潜在应用\n{application}\n")

        questions = self.questions_textbox.get("1.0", "end-1c").strip()
        if questions:
            content_parts.append(f"## 相关问题\n{questions}\n")

        full_content = "\n".join(content_parts)
        MarkdownPreviewWindow(self, title, full_content)

    def load_config(self):
        if not os.path.exists(CONFIG_FILE):
            print(f"INFO: Config file not found at {CONFIG_FILE}. Creating a default one.")
            try:
                with open(CONFIG_FILE, "w") as f:
                    json.dump({"GEMINI_API_KEY": "YOUR_API_KEY_HERE", "HTTP_PROXY": ""}, f, indent=4)
                self.api_key = None
                self.proxy_url = None
            except Exception as e:
                print(f"ERROR: Could not create default config file: {e}")
            return

        try:
            with open(CONFIG_FILE, "r") as f:
                config = json.load(f)
                self.api_key = config.get("GEMINI_API_KEY")
                self.proxy_url = config.get("HTTP_PROXY")
                if not self.api_key or self.api_key == "YOUR_API_KEY_HERE":
                    print("ERROR: 'GEMINI_API_KEY' not found or is default in config.json.")
                    self.api_key = None
        except (json.JSONDecodeError, IOError) as e:
            print(f"ERROR: Could not decode JSON from {CONFIG_FILE}: {e}")
            self.api_key = None
            self.proxy_url = None

    def update_thinking_engine_config(self):
        """更新思维引擎的配置"""
        if hasattr(self, 'thinking_engine'):
            self.thinking_engine.api_key = self.api_key
            self.thinking_engine.proxy_url = self.proxy_url

    def start_smart_analysis(self):
        """启动智能分析 - 连接用户操作与AI分析功能"""
        # 检查是否有当前想法
        if not self.current_idea_id:
            self.show_status_message("❌ 请先选择或创建一个想法", "red")
            return

        # 检查API配置
        if not self.api_key:
            self.show_status_message("❌ 请先配置API密钥", "red")
            return

        # 更新按钮状态
        self.smart_analysis_button.configure(
            state="disabled",
            text="🧠 分析中...",
            fg_color="gray"
        )

        # 清空之前的结果
        for widget in self.ai_insights_frame.winfo_children():
            widget.destroy()

        # 显示分析中的提示
        analyzing_label = ctk.CTkLabel(
            self.ai_insights_frame,
            text="🔄 AI正在深度分析您的想法...\n请稍候",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            justify="center"
        )
        analyzing_label.pack(expand=True, pady=DesignSystem.SPACING_LG)

        # 在后台线程中执行分析
        import threading
        threading.Thread(target=self._perform_smart_analysis, daemon=True).start()

    def _perform_smart_analysis(self):
        """在后台线程中执行智能分析"""
        try:
            # 获取或创建当前想法的IdeaNode
            current_node = self._get_or_create_current_idea_node()

            # 更新思维引擎配置
            self.update_thinking_engine_config()

            # 执行AI分析
            analyzed_node = self.thinking_engine.analyze_idea_node_sync(
                current_node.id,
                self.idea_nodes
            )

            # 在主线程中更新UI
            self.after(0, self._display_analysis_results, analyzed_node)

        except Exception as e:
            error_msg = f"分析失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.after(0, self._display_analysis_error, error_msg)

    def _get_or_create_current_idea_node(self):
        """获取或创建当前想法的IdeaNode"""
        if self.current_idea_id in self.idea_nodes:
            return self.idea_nodes[self.current_idea_id]

        # 如果不存在，从当前编辑内容创建
        current_idea = next((i for i in self.ideas if i["id"] == self.current_idea_id), None)
        if current_idea:
            node = self.node_manager.convert_legacy_idea_to_node(current_idea)
        else:
            # 从当前编辑器内容创建新节点
            content_parts = []
            if self.core_idea_entry.get().strip():
                content_parts.append(f"# {self.core_idea_entry.get()}")
            if self.context_textbox.get("1.0", "end-1c").strip():
                content_parts.append(f"## 背景\n{self.context_textbox.get('1.0', 'end-1c')}")
            if self.application_textbox.get("1.0", "end-1c").strip():
                content_parts.append(f"## 应用\n{self.application_textbox.get('1.0', 'end-1c')}")
            if self.questions_textbox.get("1.0", "end-1c").strip():
                content_parts.append(f"## 问题\n{self.questions_textbox.get('1.0', 'end-1c')}")

            content = "\n\n".join(content_parts)
            node = IdeaNode(content=content, node_id=str(self.current_idea_id))

            # 添加当前标签
            node.tags = [v.get() for v in self.tag_checkboxes.values() if v.get()]

        # 保存到节点字典
        self.idea_nodes[node.id] = node
        return node

    def _display_analysis_results(self, analyzed_node):
        """显示AI分析结果"""
        # 清空现有内容
        for widget in self.ai_insights_frame.winfo_children():
            widget.destroy()

        # 恢复按钮状态
        self.smart_analysis_button.configure(
            state="normal",
            text="🧠 深度分析当前想法",
            fg_color=DesignSystem.ACCENT_PURPLE
        )

        analysis = analyzed_node.ai_analysis

        # 核心观点
        if analysis.get("core_idea"):
            core_frame = ctk.CTkFrame(self.ai_insights_frame, fg_color="transparent")
            core_frame.pack(fill="x", padx=5, pady=5)

            ctk.CTkLabel(
                core_frame,
                text="💡 核心观点",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=DesignSystem.ACCENT_BLUE
            ).pack(anchor="w")

            ctk.CTkLabel(
                core_frame,
                text=analysis["core_idea"],
                font=ctk.CTkFont(size=12),
                text_color=DesignSystem.TEXT_PRIMARY,
                wraplength=300,
                justify="left"
            ).pack(anchor="w", pady=(2, 10))

        # 延展问题
        if analysis.get("potential_questions"):
            questions_frame = ctk.CTkFrame(self.ai_insights_frame, fg_color="transparent")
            questions_frame.pack(fill="x", padx=5, pady=5)

            ctk.CTkLabel(
                questions_frame,
                text="❓ 延展思考",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=DesignSystem.ACCENT_GREEN
            ).pack(anchor="w")

            for i, question in enumerate(analysis["potential_questions"][:3], 1):
                ctk.CTkLabel(
                    questions_frame,
                    text=f"{i}. {question}",
                    font=ctk.CTkFont(size=11),
                    text_color=DesignSystem.TEXT_SECONDARY,
                    wraplength=300,
                    justify="left"
                ).pack(anchor="w", pady=1)

        # 建议标签
        if analysis.get("suggested_tags"):
            tags_frame = ctk.CTkFrame(self.ai_insights_frame, fg_color="transparent")
            tags_frame.pack(fill="x", padx=5, pady=5)

            ctk.CTkLabel(
                tags_frame,
                text="🏷️ 建议标签",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=DesignSystem.ACCENT_ORANGE
            ).pack(anchor="w")

            tags_container = ctk.CTkFrame(tags_frame, fg_color="transparent")
            tags_container.pack(fill="x", pady=2)

            for tag in analysis["suggested_tags"][:5]:
                tag_btn = ctk.CTkButton(
                    tags_container,
                    text=f"#{tag}",
                    font=ctk.CTkFont(size=10),
                    height=24,
                    fg_color=DesignSystem.ACCENT_BLUE,
                    hover_color=DesignSystem.ACCENT_BLUE_LIGHT,
                    command=lambda t=tag: self._add_suggested_tag(t)
                )
                tag_btn.pack(side="left", padx=2, pady=1)

        # 情感分析和复杂度
        meta_frame = ctk.CTkFrame(self.ai_insights_frame, fg_color="transparent")
        meta_frame.pack(fill="x", padx=5, pady=5)

        sentiment = analysis.get("sentiment", "中立")
        complexity = analysis.get("complexity_level", 1)
        confidence = analysis.get("confidence_score", 0.0)

        meta_text = f"📊 情感倾向: {sentiment} | 复杂度: {complexity}/5 | 置信度: {confidence:.1%}"
        ctk.CTkLabel(
            meta_frame,
            text=meta_text,
            font=ctk.CTkFont(size=10),
            text_color=DesignSystem.TEXT_SECONDARY
        ).pack(anchor="w")

        # 自动发现连接
        self._discover_and_display_connections(analyzed_node)

        # 保存分析结果
        self.save_ideas()
        self.show_status_message("✅ AI分析完成，已发现潜在连接", "green", 3000)

    def _display_analysis_error(self, error_msg):
        """显示分析错误"""
        # 清空现有内容
        for widget in self.ai_insights_frame.winfo_children():
            widget.destroy()

        # 恢复按钮状态
        self.smart_analysis_button.configure(
            state="normal",
            text="🧠 深度分析当前想法",
            fg_color=DesignSystem.ACCENT_PURPLE
        )

        # 显示错误信息
        error_label = ctk.CTkLabel(
            self.ai_insights_frame,
            text=f"❌ {error_msg}\n\n请检查网络连接和API配置",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color="red",
            justify="center"
        )
        error_label.pack(expand=True, pady=DesignSystem.SPACING_LG)

        self.show_status_message(f"❌ {error_msg}", "red", 5000)

    def _add_suggested_tag(self, tag):
        """添加建议的标签"""
        # 检查标签是否已存在
        if tag not in self.tag_checkboxes:
            # 添加新标签到选择区域
            self.add_new_tag_to_selection(new_tag_name=tag, auto_select=True)
        else:
            # 如果已存在，直接选中
            self.tag_checkboxes[tag].set(tag)

        self.show_status_message(f"✅ 已添加标签: {tag}", "green", 2000)

    def _discover_and_display_connections(self, analyzed_node):
        """发现并显示连接"""
        # 发现潜在连接
        discovered_connections = self.connection_engine.discover_connections(
            analyzed_node,
            self.idea_nodes
        )

        if discovered_connections:
            # 在AI洞察区域添加连接部分
            connections_frame = ctk.CTkFrame(self.ai_insights_frame, fg_color="transparent")
            connections_frame.pack(fill="x", padx=5, pady=10)

            ctk.CTkLabel(
                connections_frame,
                text="🔗 发现的连接",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=DesignSystem.ACCENT_PURPLE
            ).pack(anchor="w")

            # 显示前5个最强连接
            for i, conn in enumerate(discovered_connections[:5], 1):
                target_node = self.idea_nodes.get(conn["node_id"])
                if target_node:
                    conn_item_frame = ctk.CTkFrame(connections_frame, fg_color=DesignSystem.CARD_BG)
                    conn_item_frame.pack(fill="x", pady=2)

                    # 连接信息
                    conn_text = f"{i}. {target_node.ai_analysis.get('core_idea', target_node.content[:30])}..."
                    conn_detail = f"   {conn['relation_type']} (强度: {conn['strength']:.2f}) - {conn['reason']}"

                    conn_label = ctk.CTkLabel(
                        conn_item_frame,
                        text=conn_text,
                        font=ctk.CTkFont(size=11, weight="bold"),
                        text_color=DesignSystem.TEXT_PRIMARY,
                        anchor="w"
                    )
                    conn_label.pack(anchor="w", padx=5, pady=2)

                    detail_label = ctk.CTkLabel(
                        conn_item_frame,
                        text=conn_detail,
                        font=ctk.CTkFont(size=10),
                        text_color=DesignSystem.TEXT_SECONDARY,
                        anchor="w"
                    )
                    detail_label.pack(anchor="w", padx=5, pady=(0, 2))

                    # 建立连接按钮
                    establish_btn = ctk.CTkButton(
                        conn_item_frame,
                        text="建立连接",
                        font=ctk.CTkFont(size=10),
                        height=24,
                        width=80,
                        fg_color=DesignSystem.ACCENT_GREEN,
                        hover_color=DesignSystem.ACCENT_GREEN_LIGHT,
                        command=lambda c=conn: self._establish_connection(analyzed_node, c)
                    )
                    establish_btn.pack(anchor="e", padx=5, pady=2)

            # 一键建立所有强连接按钮
            if len(discovered_connections) > 1:
                establish_all_btn = ctk.CTkButton(
                    connections_frame,
                    text=f"🔗 建立所有强连接 ({len([c for c in discovered_connections if c['strength'] > 0.7])}个)",
                    font=ctk.CTkFont(size=11, weight="bold"),
                    height=32,
                    fg_color=DesignSystem.ACCENT_PURPLE,
                    hover_color=DesignSystem.ACCENT_PURPLE_LIGHT,
                    command=lambda: self._establish_strong_connections(analyzed_node, discovered_connections)
                )
                establish_all_btn.pack(fill="x", pady=5)

    def _establish_connection(self, source_node, connection_data):
        """建立单个连接"""
        target_node_id = connection_data["node_id"]
        if target_node_id in self.idea_nodes:
            target_node = self.idea_nodes[target_node_id]

            # 建立双向连接
            source_node.add_connection(
                target_node_id,
                connection_data["relation_type"],
                connection_data["strength"]
            )

            target_node.add_connection(
                source_node.id,
                self.connection_engine._get_reverse_relation(connection_data["relation_type"]),
                connection_data["strength"]
            )

            # 保存并更新UI
            self.save_ideas()
            self.show_status_message(
                f"✅ 已建立连接: {connection_data['relation_type']}",
                "green",
                2000
            )

            # 更新图谱
            self.update_graph()

    def _establish_strong_connections(self, source_node, all_connections):
        """建立所有强连接（强度>0.7）"""
        strong_connections = [c for c in all_connections if c["strength"] > 0.7]
        established_count = 0

        for conn in strong_connections:
            try:
                self._establish_connection(source_node, conn)
                established_count += 1
            except Exception as e:
                print(f"建立连接失败: {e}")

        if established_count > 0:
            self.show_status_message(
                f"✅ 已建立 {established_count} 个强连接",
                "green",
                3000
            )

    def discover_emergent_insights(self):
        """发现涌现洞察 - 从想法网络中发现模式和智慧"""
        # 检查是否有足够的想法节点
        if len(self.idea_nodes) < 3:
            self.show_status_message("❌ 需要至少3个想法才能发现模式", "red")
            return

        # 更新按钮状态
        self.emergence_button.configure(
            state="disabled",
            text="🌟 分析中...",
            fg_color="gray"
        )

        # 清空之前的结果
        for widget in self.emergence_insights_frame.winfo_children():
            widget.destroy()

        # 显示分析中的提示
        analyzing_label = ctk.CTkLabel(
            self.emergence_insights_frame,
            text="🔄 正在分析思维网络...\n发现模式和洞察中",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color=DesignSystem.TEXT_SECONDARY,
            justify="center"
        )
        analyzing_label.pack(expand=True, pady=DesignSystem.SPACING_LG)

        # 在后台线程中执行分析
        import threading
        threading.Thread(target=self._perform_emergence_analysis, daemon=True).start()

    def _perform_emergence_analysis(self):
        """在后台线程中执行涌现分析"""
        try:
            # 执行涌现洞察发现
            insights = self.emergence_engine.discover_emergent_insights(self.idea_nodes)

            # 在主线程中更新UI
            self.after(0, self._display_emergence_insights, insights)

        except Exception as e:
            error_msg = f"洞察发现失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.after(0, self._display_emergence_error, error_msg)

    def _display_emergence_insights(self, insights):
        """显示涌现洞察结果"""
        # 清空现有内容
        for widget in self.emergence_insights_frame.winfo_children():
            widget.destroy()

        # 恢复按钮状态
        self.emergence_button.configure(
            state="normal",
            text="🌟 发现思维模式",
            fg_color=DesignSystem.ACCENT_ORANGE
        )

        # 显示聚类洞察
        if insights.get("clusters"):
            self._display_cluster_insights(insights["clusters"])

        # 显示思维模式
        if insights.get("patterns"):
            self._display_pattern_insights(insights["patterns"])

        # 显示总结
        total_insights = len(insights.get("clusters", [])) + len(insights.get("patterns", []))
        if total_insights > 0:
            summary_frame = ctk.CTkFrame(self.emergence_insights_frame, fg_color="transparent")
            summary_frame.pack(fill="x", padx=5, pady=10)

            ctk.CTkLabel(
                summary_frame,
                text=f"🎯 共发现 {total_insights} 个洞察",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=DesignSystem.ACCENT_PURPLE
            ).pack(anchor="w")

            self.show_status_message("✅ 涌现洞察发现完成", "green", 3000)
        else:
            # 没有发现洞察
            no_insights_label = ctk.CTkLabel(
                self.emergence_insights_frame,
                text="🤔 暂未发现明显的思维模式\n\n建议：\n• 增加更多想法\n• 建立更多连接\n• 使用AI分析功能",
                font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
                text_color=DesignSystem.TEXT_SECONDARY,
                justify="center"
            )
            no_insights_label.pack(expand=True, pady=DesignSystem.SPACING_LG)

    def _display_cluster_insights(self, clusters):
        """显示聚类洞察"""
        if not clusters:
            return

        cluster_frame = ctk.CTkFrame(self.emergence_insights_frame, fg_color="transparent")
        cluster_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(
            cluster_frame,
            text="🔍 想法聚类",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=DesignSystem.ACCENT_BLUE
        ).pack(anchor="w")

        for i, cluster in enumerate(clusters[:3], 1):  # 显示前3个聚类
            cluster_item = ctk.CTkFrame(cluster_frame, fg_color=DesignSystem.CARD_BG)
            cluster_item.pack(fill="x", pady=2)

            # 聚类信息
            cluster_info = f"聚类 {i}: {cluster['size']} 个想法 (强度: {cluster['strength']:.2f})"
            themes = ", ".join(cluster["themes"]["dominant_themes"][:2])
            if themes:
                cluster_info += f"\n主题: {themes}"

            ctk.CTkLabel(
                cluster_item,
                text=cluster_info,
                font=ctk.CTkFont(size=11),
                text_color=DesignSystem.TEXT_PRIMARY,
                justify="left"
            ).pack(anchor="w", padx=5, pady=3)

            # 显示聚类洞察
            for insight in cluster.get("insights", [])[:2]:
                insight_text = f"💡 {insight['content']}"
                ctk.CTkLabel(
                    cluster_item,
                    text=insight_text,
                    font=ctk.CTkFont(size=10),
                    text_color=DesignSystem.TEXT_SECONDARY,
                    wraplength=280,
                    justify="left"
                ).pack(anchor="w", padx=10, pady=1)

    def _display_pattern_insights(self, patterns):
        """显示模式洞察"""
        if not patterns:
            return

        pattern_frame = ctk.CTkFrame(self.emergence_insights_frame, fg_color="transparent")
        pattern_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(
            pattern_frame,
            text="🧠 思维模式",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=DesignSystem.ACCENT_GREEN
        ).pack(anchor="w")

        for pattern in patterns[:3]:  # 显示前3个模式
            pattern_item = ctk.CTkFrame(pattern_frame, fg_color=DesignSystem.CARD_BG)
            pattern_item.pack(fill="x", pady=2)

            # 模式信息
            pattern_info = f"{pattern['type']}: {pattern['pattern']}"
            ctk.CTkLabel(
                pattern_item,
                text=pattern_info,
                font=ctk.CTkFont(size=11, weight="bold"),
                text_color=DesignSystem.TEXT_PRIMARY,
                justify="left"
            ).pack(anchor="w", padx=5, pady=2)

            # 建议
            if pattern.get("suggestion"):
                suggestion_text = f"💭 建议: {pattern['suggestion']}"
                ctk.CTkLabel(
                    pattern_item,
                    text=suggestion_text,
                    font=ctk.CTkFont(size=10),
                    text_color=DesignSystem.TEXT_SECONDARY,
                    wraplength=280,
                    justify="left"
                ).pack(anchor="w", padx=10, pady=(0, 3))

    def _display_emergence_error(self, error_msg):
        """显示涌现分析错误"""
        # 清空现有内容
        for widget in self.emergence_insights_frame.winfo_children():
            widget.destroy()

        # 恢复按钮状态
        self.emergence_button.configure(
            state="normal",
            text="🌟 发现思维模式",
            fg_color=DesignSystem.ACCENT_ORANGE
        )

        # 显示错误信息
        error_label = ctk.CTkLabel(
            self.emergence_insights_frame,
            text=f"❌ {error_msg}\n\n请检查想法数据",
            font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
            text_color="red",
            justify="center"
        )
        error_label.pack(expand=True, pady=DesignSystem.SPACING_LG)

        self.show_status_message(f"❌ {error_msg}", "red", 5000)

    def _configure_ai_model(self):
        if not self.api_key:
            print("ERROR: API Key not configured. Please check settings.")
            return None
        
        if self.proxy_url:
            os.environ['HTTP_PROXY'] = self.proxy_url
            os.environ['HTTPS_PROXY'] = self.proxy_url
        else:
            if 'HTTP_PROXY' in os.environ: del os.environ['HTTP_PROXY']
            if 'HTTPS_PROXY' in os.environ: del os.environ['HTTPS_PROXY']

        genai.configure(api_key=self.api_key)
        return genai.GenerativeModel('gemini-1.5-flash')

    def start_suggest_tags_ai(self):
        core_idea = self.core_idea_entry.get()
        if not self.api_key:
            self.show_status_message("❌ 请先配置API密钥", "red")
            return
        if not core_idea:
            self.show_status_message("❌ 请先输入核心想法", "red")
            return

        self.ai_suggest_button.configure(state="disabled", text="思考中...")
        threading.Thread(target=self.thread_suggest_tags_ai, args=(core_idea,), daemon=True).start()

    def regenerate_tags_ai(self):
        """重新生成标签建议"""
        self.start_suggest_tags_ai()

    def start_semantic_analysis(self, idea_id):
        """启动语义分析（异步）"""
        if not self.api_key:
            return

        idea = next((i for i in self.ideas if i['id'] == idea_id), None)
        if not idea:
            return

        # 在后台线程中进行语义分析
        threading.Thread(target=self.thread_semantic_analysis, args=(idea_id,), daemon=True).start()

    def thread_semantic_analysis(self, idea_id):
        """在后台线程中进行语义分析"""
        try:
            idea = next((i for i in self.ideas if i['id'] == idea_id), None)
            if not idea:
                return

            # 获取完整文本内容
            full_text = f"{idea.get('core_idea', '')} {idea.get('context', '')} {idea.get('application', '')} {idea.get('questions', '')}"

            if not full_text.strip():
                return

            # 构建语义分析提示词
            analysis_prompt = f"""
请对以下文本进行深度语义分析，提取关键信息：

文本内容：
{full_text}

请以JSON格式返回分析结果，包含以下字段：
1. "auto_keywords": 自动提取的5-8个关键词（数组）
2. "concepts": 识别的核心概念（数组）
3. "themes": 主要主题（数组）
4. "complexity_score": 内容复杂度评分（1-10）
5. "importance_score": 重要性评分（1-10）
6. "summary": 一句话总结

请确保返回有效的JSON格式。
"""

            model = self._configure_ai_model()
            if not model:
                return

            response = model.generate_content(analysis_prompt)

            if response and response.text:
                # 尝试解析JSON响应
                try:
                    import re
                    # 提取JSON部分
                    json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                    if json_match:
                        analysis_result = json.loads(json_match.group())

                        # 更新想法的语义信息
                        self.update_idea_semantics(idea_id, analysis_result)

                        # 在主线程中更新UI
                        self.after(0, lambda: self.show_status_message("🧠 语义分析完成", "blue", 2000))

                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    print(f"响应内容: {response.text}")

        except Exception as e:
            print(f"语义分析错误: {e}")
            self.after(0, lambda: self.show_status_message("❌ 语义分析失败", "red", 2000))

    def update_idea_semantics(self, idea_id, analysis_result):
        """更新想法的语义信息"""
        idea_idx = next((i for i, item in enumerate(self.ideas) if item["id"] == idea_id), None)
        if idea_idx is not None:
            idea = self.ideas[idea_idx]

            # 更新语义信息
            idea['auto_keywords'] = analysis_result.get('auto_keywords', [])
            idea['concepts'] = analysis_result.get('concepts', [])
            idea['themes'] = analysis_result.get('themes', [])
            idea['complexity_score'] = analysis_result.get('complexity_score', 0)
            idea['importance_score'] = analysis_result.get('importance_score', 0)
            idea['ai_summary'] = analysis_result.get('summary', '')

            # 保存更新
            self.save_ideas()

            # 启动语义连接发现
            self.discover_semantic_connections(idea_id)

    def discover_semantic_connections(self, idea_id):
        """发现语义连接"""
        try:
            current_idea = next((i for i in self.ideas if i['id'] == idea_id), None)
            if not current_idea:
                return

            # 获取当前想法的语义特征
            current_keywords = set(current_idea.get('auto_keywords', []) + current_idea.get('keywords', []))
            current_concepts = set(current_idea.get('concepts', []))
            current_themes = set(current_idea.get('themes', []))

            semantic_connections = []

            # 遍历所有其他想法，计算语义相似度
            for other_idea in self.ideas:
                if other_idea['id'] == idea_id or other_idea.get('is_deleted', False):
                    continue

                # 计算语义相似度
                other_keywords = set(other_idea.get('auto_keywords', []) + other_idea.get('keywords', []))
                other_concepts = set(other_idea.get('concepts', []))
                other_themes = set(other_idea.get('themes', []))

                # 计算各维度的相似度
                keyword_similarity = len(current_keywords & other_keywords) / max(len(current_keywords | other_keywords), 1)
                concept_similarity = len(current_concepts & other_concepts) / max(len(current_concepts | other_concepts), 1)
                theme_similarity = len(current_themes & other_themes) / max(len(current_themes | other_themes), 1)

                # 综合相似度评分
                overall_similarity = (keyword_similarity * 0.4 + concept_similarity * 0.4 + theme_similarity * 0.2)

                # 如果相似度超过阈值，建立连接
                if overall_similarity > 0.3:  # 可调整的阈值
                    semantic_connections.append({
                        'id': other_idea['id'],
                        'similarity': overall_similarity,
                        'connection_type': 'semantic',
                        'strength': self.calculate_connection_strength(overall_similarity),
                        'shared_elements': {
                            'keywords': list(current_keywords & other_keywords),
                            'concepts': list(current_concepts & other_concepts),
                            'themes': list(current_themes & other_themes)
                        }
                    })

            # 按相似度排序，保留前10个最相关的连接
            semantic_connections.sort(key=lambda x: x['similarity'], reverse=True)
            semantic_connections = semantic_connections[:10]

            # 更新当前想法的语义连接
            idea_idx = next((i for i, item in enumerate(self.ideas) if item["id"] == idea_id), None)
            if idea_idx is not None:
                self.ideas[idea_idx]['semantic_connections'] = semantic_connections
                self.save_ideas()

                # 在主线程中更新UI
                if semantic_connections:
                    self.after(0, lambda: self.show_status_message(f"🔗 发现 {len(semantic_connections)} 个语义连接", "blue", 2000))
                    self.after(0, lambda: self.update_smart_recommendations(idea_id))

        except Exception as e:
            print(f"语义连接发现错误: {e}")

    def calculate_connection_strength(self, similarity):
        """计算连接强度"""
        if similarity >= 0.7:
            return "strong"
        elif similarity >= 0.5:
            return "medium"
        elif similarity >= 0.3:
            return "weak"
        else:
            return "very_weak"

    def update_smart_recommendations(self, idea_id=None):
        """更新智能推荐面板"""
        # 清空现有推荐
        for widget in self.smart_recommendations_frame.winfo_children():
            widget.destroy()

        if not idea_id and self.current_idea_id:
            idea_id = self.current_idea_id

        if not idea_id:
            # 显示通用推荐
            self.show_general_recommendations()
            return

        current_idea = next((i for i in self.ideas if i['id'] == idea_id), None)
        if not current_idea:
            return

        # 显示语义连接推荐
        semantic_connections = current_idea.get('semantic_connections', [])
        if semantic_connections:
            self.show_semantic_recommendations(semantic_connections[:5])  # 显示前5个

        # 显示概念扩展推荐
        self.show_concept_expansion_recommendations(current_idea)

        # 显示结构洞推荐
        self.show_structural_hole_recommendations(current_idea)

    def show_semantic_recommendations(self, connections):
        """显示语义连接推荐"""
        if not connections:
            return

        # 标题
        title_label = ctk.CTkLabel(
            self.smart_recommendations_frame,
            text="🔗 相关想法",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        for conn in connections:
            related_idea = next((i for i in self.ideas if i['id'] == conn['id']), None)
            if not related_idea:
                continue

            # 连接强度颜色
            strength_colors = {
                "strong": "#32CD32",
                "medium": "#FFD700",
                "weak": "#FFA500",
                "very_weak": "#FF6347"
            }

            conn_frame = ctk.CTkFrame(self.smart_recommendations_frame, fg_color="#2B2B2B")
            conn_frame.pack(fill="x", padx=10, pady=2)

            # 想法标题（可点击）
            idea_button = ctk.CTkButton(
                conn_frame,
                text=f"💡 {related_idea.get('core_idea', '')[:40]}...",
                command=lambda id=conn['id']: self.display_idea(id),
                fg_color="transparent",
                hover_color="#404040",
                anchor="w"
            )
            idea_button.pack(fill="x", padx=5, pady=2)

            # 连接信息
            shared_elements = conn.get('shared_elements', {})
            info_text = f"相似度: {conn['similarity']:.2f} | 强度: {conn['strength']}"
            if shared_elements.get('keywords'):
                info_text += f" | 共同关键词: {', '.join(shared_elements['keywords'][:3])}"

            info_label = ctk.CTkLabel(
                conn_frame,
                text=info_text,
                font=ctk.CTkFont(size=10),
                text_color=strength_colors.get(conn['strength'], "gray")
            )
            info_label.pack(anchor="w", padx=5, pady=(0, 5))

    def show_concept_expansion_recommendations(self, current_idea):
        """显示概念扩展推荐"""
        concepts = current_idea.get('concepts', [])
        if not concepts:
            return

        title_label = ctk.CTkLabel(
            self.smart_recommendations_frame,
            text="🎯 概念扩展",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(15, 5))

        for concept in concepts[:3]:  # 显示前3个概念
            concept_frame = ctk.CTkFrame(self.smart_recommendations_frame, fg_color="#1E1E1E")
            concept_frame.pack(fill="x", padx=10, pady=2)

            concept_button = ctk.CTkButton(
                concept_frame,
                text=f"🔍 探索 '{concept}' 的更多可能性",
                command=lambda c=concept: self.explore_concept(c),
                fg_color="#4169E1",
                hover_color="#1E90FF",
                font=ctk.CTkFont(size=11)
            )
            concept_button.pack(fill="x", padx=5, pady=5)

    def show_structural_hole_recommendations(self, current_idea):
        """显示结构洞推荐（发现知识空白）"""
        title_label = ctk.CTkLabel(
            self.smart_recommendations_frame,
            text="🕳️ 知识空白",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(15, 5))

        # 简单的结构洞发现：找到相关但未连接的概念
        current_themes = set(current_idea.get('themes', []))

        # 找到其他想法中出现但当前想法中没有的主题
        all_themes = set()
        for idea in self.ideas:
            if idea['id'] != current_idea['id'] and not idea.get('is_deleted', False):
                all_themes.update(idea.get('themes', []))

        missing_themes = all_themes - current_themes

        if missing_themes:
            for theme in list(missing_themes)[:2]:  # 显示前2个缺失主题
                hole_frame = ctk.CTkFrame(self.smart_recommendations_frame, fg_color="#2B2B2B")
                hole_frame.pack(fill="x", padx=10, pady=2)

                hole_button = ctk.CTkButton(
                    hole_frame,
                    text=f"❓ 考虑 '{theme}' 与当前想法的关系",
                    command=lambda t=theme: self.explore_theme_connection(current_idea['id'], t),
                    fg_color="#9370DB",
                    hover_color="#8A2BE2",
                    font=ctk.CTkFont(size=11)
                )
                hole_button.pack(fill="x", padx=5, pady=5)

    def show_general_recommendations(self):
        """显示通用推荐"""
        title_label = ctk.CTkLabel(
            self.smart_recommendations_frame,
            text="💡 智能建议",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        suggestion_label = ctk.CTkLabel(
            self.smart_recommendations_frame,
            text="选择一个想法来查看智能推荐和语义连接",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        suggestion_label.pack(anchor="w", padx=10, pady=5)

    def explore_concept(self, concept):
        """探索概念"""
        # 这里可以实现概念探索功能，比如生成相关问题或建议
        self.show_status_message(f"🔍 正在探索概念: {concept}", "blue", 2000)
        # TODO: 实现AI驱动的概念探索

    def explore_theme_connection(self, idea_id, theme):
        """探索主题连接"""
        self.show_status_message(f"🔗 正在分析 '{theme}' 的连接可能性", "blue", 2000)
        # TODO: 实现AI驱动的主题连接分析

    def thread_suggest_tags_ai(self, core_idea):
        # 根据选择的风格生成不同的提示词
        style = self.ai_style_combo.get()
        style_prompts = {
            "通用风格": "分析以下想法，提供5个最相关的通用关键词。",
            "技术风格": "从技术角度分析以下想法，提供5个技术相关的关键词，如编程语言、框架、技术概念等。",
            "商业风格": "从商业角度分析以下想法，提供5个商业相关的关键词，如市场、盈利模式、用户群体等。",
            "创意风格": "从创意角度分析以下想法，提供5个富有创意和想象力的关键词。",
            "学术风格": "从学术研究角度分析以下想法，提供5个学术相关的关键词，如研究方法、理论框架等。"
        }

        style_prompt = style_prompts.get(style, style_prompts["通用风格"])
        prompt = f"{style_prompt}严格按逗号分隔格式返回，例如: AI,教育,市场营销\n核心想法: {core_idea}\n背景: {self.context_textbox.get('1.0', 'end-1c')}"
        stream_window = AIStreamWindow(self, f"为 “{core_idea}” 建议标签 ({style})")
        
        try:
            model = self._configure_ai_model()
            if not model: raise Exception("AI Model could not be configured.")
            response_stream = model.generate_content(prompt, stream=True)
            
            full_response = ""
            for chunk in response_stream:
                if chunk.text:
                    full_response += chunk.text
                    self.after(0, stream_window.update_content, chunk.text)
            
            self.after(0, self.finalize_suggest_tags, full_response, stream_window)
        except Exception as e:
            error_message = f"AI call failed: {e}"
            print(error_message)
            self.after(0, self.finalize_suggest_tags, f"Error: {error_message}", stream_window)

    def finalize_suggest_tags(self, response_text, stream_window):
        if stream_window.winfo_exists(): stream_window.destroy()
        print(f"DEBUG: AI suggested tags response: '{response_text}'")
        
        if response_text and not response_text.startswith("Error:"):
            tags = [t.strip() for t in response_text.strip().split(',') if t.strip()]
            if tags:
                for tag in tags: self.add_new_tag_to_selection(new_tag_name=tag, auto_select=True)
            else:
                print("INFO: AI returned empty or invalid tags.")
        
        self.ai_suggest_button.configure(state="normal", text="🤖 AI建议")

    def start_expand_idea_ai(self):
        if not self.api_key:
            print("ERROR: API Key not configured. Please open Settings."); return
        if not self.current_idea_id:
            print("ERROR: No idea selected"); return
        
        idea = next((i for i in self.ideas if i['id'] == self.current_idea_id), None)
        if not idea: return

        self.ai_expand_button.configure(state="disabled", text="分析中...")
        threading.Thread(target=self.thread_expand_idea_ai, args=(idea,), daemon=True).start()

    def thread_expand_idea_ai(self, idea):
        idea_content = f"核心想法: {idea.get('core_idea', '')}\n背景/来源: {idea.get('context', '')}\n潜在应用: {idea.get('application', '')}\n相关问题: {idea.get('questions', '')}"
        prompt = f"你是一位顶级的创意策略师。深入分析以下想法，并严格按照下面的格式和标题返回创意分析报告，不要添加任何额外解释。\n\n【扩展描述】\n(在此扩展核心想法为一段约100字的生动描述)\n\n【深度提问】\n(从用户价值、技术可行性、市场潜力和潜在风险四个角度，各提出一个深刻问题，以“-”开头)\n\n【应用构思】\n(构思一到两个具体的可执行应用场景并简要描述)\n\n--- 待分析的想法如下 ---\n{idea_content}"
        stream_window = AIStreamWindow(self, f"扩展 “{idea.get('core_idea', '')}”")

        try:
            model = self._configure_ai_model()
            if not model: raise Exception("AI Model could not be configured.")
            response_stream = model.generate_content(prompt, stream=True)

            full_response = ""
            for chunk in response_stream:
                if chunk.text:
                    full_response += chunk.text
                    self.after(0, stream_window.update_content, chunk.text)
            
            self.after(0, self.finalize_expand_idea, idea, full_response, stream_window)
        except Exception as e:
            error_message = f"AI call failed: {e}"
            print(error_message)
            self.after(0, self.finalize_expand_idea, idea, f"Error: {error_message}", stream_window)

    def finalize_expand_idea(self, idea, response_text, stream_window):
        if stream_window.winfo_exists(): stream_window.destroy()
        print(f"DEBUG: AI expansion response: '{response_text}'")

        if response_text and not response_text.startswith("Error:"):
            AIResponseWindow(self, title_text=idea.get('core_idea', ''), content_text=response_text)
        else:
            print("INFO: AI returned an empty or error response for expansion.")
        
        self.ai_expand_button.configure(state="normal", text="🤖 AI 扩展想法")
    
    def start_generate_title_ai(self):
        if not self.api_key:
            print("ERROR: API Key not configured. Please open Settings."); return
        
        content = self.get_full_content_for_summary()
        if not content:
            print("INFO: No content to summarize for a title."); return

        self.ai_title_button.configure(state="disabled", text="生成中...")
        threading.Thread(target=self.thread_generate_title_ai, args=(content,), daemon=True).start()

    def get_full_content_for_summary(self):
        context = self.context_textbox.get("1.0", "end-1c").strip()
        application = self.application_textbox.get("1.0", "end-1c").strip()
        questions = self.questions_textbox.get("1.0", "end-1c").strip()
        
        full_text = f"背景/来源:\n{context}\n\n潜在应用:\n{application}\n\n相关问题:\n{questions}"
        return full_text.strip()

    def thread_generate_title_ai(self, content):
        prompt = f"根据以下内容，为其生成一个简洁、精炼、不超过15个字的核心标题。直接返回标题文本，不要包含任何引号或多余的解释。\n\n--- 内容如下 ---\n{content}"
        stream_window = AIStreamWindow(self, "生成标题")
        
        try:
            model = self._configure_ai_model()
            if not model: raise Exception("AI Model could not be configured.")
            response_stream = model.generate_content(prompt, stream=True)

            full_response = ""
            for chunk in response_stream:
                if chunk.text:
                    full_response += chunk.text
                    self.after(0, stream_window.update_content, chunk.text)
            
            self.after(0, self.finalize_generate_title, full_response, stream_window)
        except Exception as e:
            error_message = f"AI call failed: {e}"
            print(error_message)
            self.after(0, self.finalize_generate_title, f"Error: {error_message}", stream_window)

    def finalize_generate_title(self, response_text, stream_window):
        if stream_window.winfo_exists(): stream_window.destroy()
        print(f"DEBUG: AI title generation response: '{response_text}'")

        if response_text and not response_text.startswith("Error:"):
            clean_title = response_text.strip().replace("*", "").replace("\"", "")
            self.core_idea_entry.delete(0, ctk.END)
            self.core_idea_entry.insert(0, clean_title)
        else:
            print("INFO: AI returned an empty or error response for title generation.")

        self.ai_title_button.configure(state="normal", text="✨ AI标题")

    def refresh_idea_list(self, ideas_to_display=None):
        for widget in self.idea_list_frame.winfo_children(): widget.destroy()
        active_ideas = ideas_to_display if ideas_to_display is not None else [i for i in self.ideas if not i.get('is_deleted')]
        for idea in sorted(active_ideas, key=lambda x: STATUS_LIST.index(x.get('status', DEFAULT_STATUS))):
            status_icon = idea.get('status', DEFAULT_STATUS).split(' ')[0]
            category_icon = CATEGORY_ICONS.get(idea.get('category', DEFAULT_CATEGORY), '💡')
            core_idea = idea.get('core_idea', 'N/A')

            # 创建想法按钮，包含状态和分类图标
            btn = ctk.CTkButton(
                self.idea_list_frame,
                text=f"{status_icon} {category_icon} {core_idea}",
                fg_color="transparent",
                hover_color=DesignSystem.HOVER_BG,
                anchor="w",
                command=lambda i=idea.get("id"): self.display_idea(i),
                font=ctk.CTkFont(size=DesignSystem.FONT_NORMAL),
                text_color=DesignSystem.TEXT_PRIMARY
            )
            btn.pack(fill="x", padx=5, pady=2)
    
    def display_idea(self, idea_id):
        self.current_idea_id = idea_id
        idea = next((item for item in self.ideas if item["id"] == idea_id), None)
        if not idea:
            return

        # 更新访问计数和时间
        idea_idx = next((i for i, item in enumerate(self.ideas) if item["id"] == idea_id), None)
        if idea_idx is not None:
            self.ideas[idea_idx]['access_count'] = self.ideas[idea_idx].get('access_count', 0) + 1
            self.ideas[idea_idx]['last_accessed'] = time.time()

        self.clear_form(clear_id=False)
        self.status_combo.set(idea.get("status", DEFAULT_STATUS))
        self.category_combo.set(idea.get("category", DEFAULT_CATEGORY))
        self.core_idea_entry.delete(0, ctk.END)
        self.core_idea_entry.insert(0, idea.get("core_idea", ""))
        self.update_tag_selection_area(idea.get("keywords", []))
        self.context_textbox.delete("1.0", ctk.END)
        self.context_textbox.insert("1.0", idea.get("context", ""))
        self.application_textbox.delete("1.0", ctk.END)
        self.application_textbox.insert("1.0", idea.get("application", ""))
        self.questions_textbox.delete("1.0", ctk.END)
        self.questions_textbox.insert("1.0", idea.get("questions", ""))
        self.refresh_manual_links_display(idea_id)
        self.perform_auto_link(idea_id)

        # 更新工具栏信息
        self.update_toolbar_info(idea.get("core_idea", ""))

        # 更新智能推荐
        self.update_smart_recommendations(idea_id)

        # 更新最后访问时间的哈希
        self.last_content_hash = self.get_current_content_hash()

    def save_current_idea(self):
        if not self.core_idea_entry.get():
            self.show_status_message("❌ 请输入核心想法后再保存", "red")
            return

        try:
            # 构建增强的卡片数据结构
            current_time = time.time()
            idea_data = {
                # 基本信息
                "id": self.current_idea_id or int(current_time * 1000),
                "created_at": current_time if not self.current_idea_id else None,
                "updated_at": current_time,
                "status": self.status_combo.get(),
                "category": self.category_combo.get(),

                # 核心内容（卡片的主体）
                "core_idea": self.core_idea_entry.get(),
                "context": self.context_textbox.get("1.0", "end-1c"),
                "application": self.application_textbox.get("1.0", "end-1c"),
                "questions": self.questions_textbox.get("1.0", "end-1c"),

                # 语义标识
                "keywords": [v.get() for v in self.tag_checkboxes.values() if v.get()],
                "auto_keywords": [],  # AI自动提取的关键词
                "concepts": [],       # AI识别的核心概念
                "themes": [],         # AI识别的主题

                # 结构化内容字段（类似AI角色设定）
                "profile": {
                    "title": self.core_idea_entry.get()[:50],  # 简短标题
                    "version": "1.0",
                    "language": "中文",
                    "description": self.context_textbox.get("1.0", "end-1c")[:100] + "..." if len(self.context_textbox.get("1.0", "end-1c")) > 100 else self.context_textbox.get("1.0", "end-1c")
                },
                "background": self.context_textbox.get("1.0", "end-1c"),
                "goals": [goal.strip() for goal in self.application_textbox.get("1.0", "end-1c").split('\n') if goal.strip()],
                "constraints": [],
                "implementation_plan": "",
                "success_metrics": [],
                "resources_needed": [],
                "timeline": "",
                "potential_risks": [],

                # 连接信息
                "linked_ideas": [],
                "semantic_connections": [],  # 基于语义相似性的自动连接
                "connection_strength": {},   # 连接强度评分

                # 元数据
                "word_count": len(self.get_full_text_content().split()),
                "complexity_score": 0,  # 内容复杂度评分
                "importance_score": 0,  # 重要性评分
                "access_count": 0,      # 访问次数
                "last_accessed": current_time,

                # 系统标识
                "is_deleted": False,
                "card_type": "idea",    # 卡片类型：idea, note, quote, reference等
                "source": "manual",     # 来源：manual, import, ai_generated等
                "version": 1            # 版本号，用于追踪修改历史
            }

            # 保留现有的链接信息
            if self.current_idea_id:
                idea_idx = next((i for i, item in enumerate(self.ideas) if item["id"] == self.current_idea_id), None)
                if idea_idx is not None:
                    # 保留现有的连接和元数据
                    old_idea = self.ideas[idea_idx]
                    idea_data['linked_ideas'] = old_idea.get('linked_ideas', [])
                    idea_data['semantic_connections'] = old_idea.get('semantic_connections', [])
                    idea_data['access_count'] = old_idea.get('access_count', 0) + 1
                    idea_data['created_at'] = old_idea.get('created_at', current_time)
                    idea_data['version'] = old_idea.get('version', 1) + 1
                    self.ideas[idea_idx] = idea_data
            else:
                self.ideas.append(idea_data)
                self.current_idea_id = idea_data["id"]

            # 启动AI语义分析（异步）
            self.start_semantic_analysis(idea_data["id"])

            self.save_ideas()
            self.refresh_idea_list()
            self.update_keyword_combos()
            self.update_tag_selection_area(idea_data["keywords"])
            self.update_graph()

            # 显示成功消息
            self.show_status_message("✅ 想法已保存，正在进行语义分析...", "green", 3000)

        except Exception as e:
            self.show_status_message(f"❌ 保存失败: {str(e)}", "red", 5000)

    def delete_current_idea(self):
        if not self.current_idea_id: return
        idea = next((item for item in self.ideas if item["id"] == self.current_idea_id), None)
        if idea: idea['is_deleted'] = True; self.save_ideas(); self.clear_form(); self.refresh_idea_list(); self.update_graph()

    def clear_form(self, clear_id=True):
        if clear_id: self.current_idea_id = None
        self.status_combo.set(DEFAULT_STATUS)
        self.category_combo.set(DEFAULT_CATEGORY)
        self.core_idea_entry.delete(0, 'end')
        self.update_tag_selection_area()
        self.context_textbox.delete("1.0", "end")
        self.application_textbox.delete("1.0", "end")
        self.questions_textbox.delete("1.0", "end")
        self.refresh_manual_links_display(None)
        self.core_idea_entry.focus()
    
    def open_linker_window(self):
        if self.current_idea_id: LinkerWindow(self, self.current_idea_id)

    def add_manual_links(self, source_id, target_ids):
        """添加手动链接，支持双向链接"""
        source_idea = next((i for i in self.ideas if i['id'] == source_id), None)
        if not source_idea:
            return

        if 'linked_ideas' not in source_idea:
            source_idea['linked_ideas'] = []

        for target_id in target_ids:
            if not target_id:
                continue

            target_id = int(target_id)
            target_idea = next((i for i in self.ideas if i['id'] == target_id), None)

            if not target_idea:
                continue

            # 添加从源到目标的链接
            if target_id not in [link['id'] for link in source_idea['linked_ideas']]:
                source_idea['linked_ideas'].append({'id': target_id, 'note': '', 'type': 'manual'})

            # 添加从目标到源的双向链接
            if 'linked_ideas' not in target_idea:
                target_idea['linked_ideas'] = []
            if source_id not in [link['id'] for link in target_idea['linked_ideas']]:
                target_idea['linked_ideas'].append({'id': source_id, 'note': '', 'type': 'manual'})

        self.save_ideas()
        self.refresh_manual_links_display(source_id)
        self.update_graph()
        self.show_status_message("✅ 双向链接已建立", "green", 2000)

    def remove_manual_link(self, source_id, target_id):
        """删除手动链接，支持双向删除"""
        source_idea = next((i for i in self.ideas if i['id'] == source_id), None)
        target_idea = next((i for i in self.ideas if i['id'] == target_id), None)

        # 从源想法中删除到目标的链接
        if source_idea and 'linked_ideas' in source_idea:
            source_idea['linked_ideas'] = [link for link in source_idea['linked_ideas'] if link['id'] != target_id]

        # 从目标想法中删除到源的链接
        if target_idea and 'linked_ideas' in target_idea:
            target_idea['linked_ideas'] = [link for link in target_idea['linked_ideas'] if link['id'] != source_id]

        self.save_ideas()
        self.refresh_manual_links_display(source_id)
        self.update_graph()
        self.show_status_message("✅ 双向链接已删除", "orange", 2000)

    def refresh_manual_links_display(self, idea_id):
        for widget in self.linked_items_container.winfo_children(): widget.destroy()
        if not idea_id: return
        idea = next((i for i in self.ideas if i['id'] == idea_id), None)
        if not (idea and idea.get('linked_ideas')): return
        for link_data in idea.get('linked_ideas', []):
            linked_idea = next((i for i in self.ideas if i['id'] == link_data['id']), None)
            if linked_idea:
                link_frame = ctk.CTkFrame(self.linked_items_container, fg_color="transparent"); link_frame.pack(fill="x")
                link_btn = ctk.CTkButton(link_frame, text=f"🔗 {linked_idea['core_idea']}", fg_color="transparent", anchor="w", command=lambda id=linked_idea['id']: self.display_idea(id)); link_btn.pack(side="left", expand=True, fill="x")
                remove_btn = ctk.CTkButton(link_frame, text="x", width=25, fg_color="transparent", text_color="gray", command=lambda s_id=idea_id, t_id=linked_idea['id']: self.remove_manual_link(s_id, t_id)); remove_btn.pack(side="right")

    def get_all_tags(self): return sorted(list(set(k for idea in self.ideas for k in idea.get("keywords", []))))
    def update_tag_selection_area(self, current_tags=None):
        for widget in self.tag_selection_frame.winfo_children(): widget.destroy()
        self.tag_checkboxes = {}
        for tag in self.get_all_tags():
            var = ctk.StringVar(value=tag if current_tags and tag in current_tags else ""); cb = ctk.CTkCheckBox(self.tag_selection_frame, text=tag, variable=var, onvalue=tag, offvalue=""); cb.pack(side="left", padx=5, pady=5); self.tag_checkboxes[tag] = var

    def add_new_tag_to_selection(self, event=None, new_tag_name=None, auto_select=False):
        new_tag = (new_tag_name or self.add_tag_entry.get()).strip()
        if not new_tag: return
        if new_tag in self.tag_checkboxes:
            if auto_select: self.tag_checkboxes[new_tag].set(new_tag)
        else:
            var = ctk.StringVar(value=new_tag if auto_select else ""); cb = ctk.CTkCheckBox(self.tag_selection_frame, text=new_tag, variable=var, onvalue=new_tag, offvalue=""); cb.pack(side="left", padx=5, pady=5); self.tag_checkboxes[new_tag] = var
        if not new_tag_name: self.add_tag_entry.delete(0, 'end')

    def open_tag_manager(self): TagManagerWindow(self)

    def open_graph_view(self):
        """打开图谱视图窗口"""
        if self.graph_window is None or not self.graph_window.winfo_exists():
            self.graph_window = GraphViewWindow(self)
        else:
            self.graph_window.lift()  # 将窗口提到前台
            self.graph_window.focus()

    def update_graph(self):
        """更新图谱视图（如果窗口已打开）"""
        if self.graph_window is not None and self.graph_window.winfo_exists():
            try:
                self.graph_window.draw_graph()
            except Exception as e:
                print(f"更新图谱时出错: {e}")

    def load_ideas(self):
        """加载想法数据 - 支持新旧格式转换"""
        print(f"DEBUG: Using DB_FILE = {DB_FILE}")
        if os.path.exists(DB_FILE):
            try:
                with open(DB_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 检查是否为新格式（包含IdeaNode数据）
                if isinstance(data, dict) and "idea_nodes" in data:
                    # 新格式：直接加载IdeaNode
                    self.ideas = data.get("legacy_ideas", [])
                    node_data = data.get("idea_nodes", {})
                    self.idea_nodes = {
                        node_id: IdeaNode.from_dict(node_dict)
                        for node_id, node_dict in node_data.items()
                    }
                    print(f"DEBUG: Loaded {len(self.ideas)} legacy ideas and {len(self.idea_nodes)} idea nodes")
                else:
                    # 旧格式：转换为IdeaNode
                    self.ideas = data if isinstance(data, list) else []
                    self.idea_nodes = {}

                    # 将旧想法转换为IdeaNode
                    for legacy_idea in self.ideas:
                        if not legacy_idea.get('is_deleted', False):
                            node = self.node_manager.convert_legacy_idea_to_node(legacy_idea)
                            self.idea_nodes[node.id] = node

                    print(f"DEBUG: Converted {len(self.ideas)} legacy ideas to {len(self.idea_nodes)} idea nodes")

            except (json.JSONDecodeError, IOError) as e:
                print(f"DEBUG: Error loading ideas: {e}")
                self.ideas = []
                self.idea_nodes = {}
        else:
            print("DEBUG: DB_FILE does not exist, creating empty ideas list")
            self.ideas = []
            self.idea_nodes = {}
            
            
    def save_ideas(self):
        """保存想法数据 - 新格式支持IdeaNode"""
        # 构建新格式的数据结构
        save_data = {
            "version": "2.0",
            "legacy_ideas": self.ideas,  # 保持向后兼容
            "idea_nodes": {
                node_id: node.to_dict()
                for node_id, node in self.idea_nodes.items()
            },
            "metadata": {
                "total_nodes": len(self.idea_nodes),
                "last_saved": time.time(),
                "format": "idea_node_v2"
            }
        }

        with open(DB_FILE, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=4)
    def open_recycle_bin_window(self): RecycleBinWindow(self)
    def filter_ideas(self, event=None):
        search_term = self.search_entry.get().lower()
        if search_term:
            self.refresh_idea_list([i for i in self.ideas if not i.get('is_deleted') and (search_term in i.get('core_idea','').lower() or any(search_term in t.lower() for t in i.get('keywords',[])))])
        else:
            self.refresh_idea_list()
    def update_keyword_combos(self):
        all_keywords = sorted(list(set(k for i in self.ideas if not i.get('is_deleted') for k in i.get("keywords", []))))
        if all_keywords:
            self.keyword_combo_1.configure(values=all_keywords); self.keyword_combo_2.configure(values=all_keywords)
            self.keyword_combo_1.set(all_keywords[0]); self.keyword_combo_2.set(all_keywords[1] if len(all_keywords) > 1 else "")
        else:
            self.keyword_combo_1.configure(values=[" "]); self.keyword_combo_2.configure(values=[" "]); self.keyword_combo_1.set(" "); self.keyword_combo_2.set(" ")
    def _clear_and_display_results(self, title, results):
        for widget in self.generation_result_frame.winfo_children(): widget.destroy()
        self.generation_result_frame.configure(label_text=title)
        if not results: ctk.CTkLabel(self.generation_result_frame, text="没有找到相关结果。", justify="center").pack(expand=True)
        else:
            for text, idea_id in results: btn = ctk.CTkButton(self.generation_result_frame, text=text, fg_color="transparent", anchor="w", command=lambda id=idea_id: self.display_idea(id)); btn.pack(fill="x", padx=5, pady=2)
    def perform_auto_link(self, idea_id):
        idea = next((i for i in self.ideas if i["id"] == idea_id), None)
        if not (idea and idea.get("keywords")): self._clear_and_display_results(f"与“{idea['core_idea'][:10]}...”的链接" if idea else "自动链接", []); return
        linked = [(i["core_idea"],i["id"]) for i in self.ideas if i["id"]!=idea_id and not i.get('is_deleted') and set(idea.get("keywords",[])).intersection(set(i.get("keywords",[])))]
        self._clear_and_display_results(f"与“{idea['core_idea'][:10]}...”的链接", linked)
    def perform_collision(self, event=None):
        k1, k2 = self.keyword_combo_1.get(), self.keyword_combo_2.get()
        if " " in k1 or " " in k2 or k1 == k2: return
        collided = [(f"[{','.join(i.get('keywords',[]))}] {i['core_idea']}", i['id']) for i in self.ideas if not i.get('is_deleted') and (k1 in i.get("keywords",[]) or k2 in i.get("keywords",[]))]
        self._clear_and_display_results(f"“{k1}” X “{k2}”", collided)
    def perform_random_inspiration(self):
        active = [i for i in self.ideas if not i.get('is_deleted')]
        if len(active) < 2: return
        i1, i2 = random.sample(active, 2)
        self._clear_and_display_results("随机灵感碰撞", [(f"【随机1】{i1['core_idea']}",i1['id']), (f"【随机2】{i2['core_idea']}",i2['id'])])

if __name__ == "__main__":
    # 设置为浅色主题，符合Notion风格
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")
    app = IdeaGeneratorApp()
    app.mainloop()




