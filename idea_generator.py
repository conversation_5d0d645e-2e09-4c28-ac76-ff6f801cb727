import customtkinter as ctk
import json
import time
import os
import random
import threading
from functools import partial
import networkx as nx
import matplotlib
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import google.generativeai as genai
import textwrap
import requests

# ==============================================================================
# ---- PATH SETUP & GLOBAL CONSTANTS ----
# ==============================================================================
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_FILE = os.path.join(SCRIPT_DIR, "config.json")
DB_FILE = os.path.join(SCRIPT_DIR, "ideas.json")

try:
    matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    matplotlib.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"Font setup failed, graph labels may not display correctly: {e}")

STATUS_LIST = ["🌱 种子", "💡 构思中", "🛠️ 开发中", "✅ 已完成", "📦 已归档"]
DEFAULT_STATUS = STATUS_LIST[0]
STATUS_COLORS = {
    "🌱 种子": "#6a994e", "💡 构思中": "#a98467", "🛠️ 开发中": "#fca311",
    "✅ 已完成": "#147df5", "📦 已归档": "#6c757d"
}

# ==============================================================================
# ---- HELPER WINDOW CLASSES ----
# ==============================================================================
class SettingsWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.title("网络与API设置")
        self.geometry("600x400")
        self.transient(master)
        self.grab_set()

        ctk.CTkLabel(self, text="Gemini API Key:", font=ctk.CTkFont(size=14, weight="bold")).pack(padx=20, pady=(20, 5), anchor="w")
        self.api_key_entry = ctk.CTkEntry(self, placeholder_text="在此输入你的 Gemini API Key", width=560)
        self.api_key_entry.pack(padx=20, pady=5, fill="x")
        self.api_key_entry.insert(0, self.master.api_key or "")

        ctk.CTkLabel(self, text="HTTP 代理地址 (可选):", font=ctk.CTkFont(size=14, weight="bold")).pack(padx=20, pady=(20, 5), anchor="w")
        self.proxy_entry = ctk.CTkEntry(self, placeholder_text="例如: http://127.0.0.1:7890", width=560)
        self.proxy_entry.pack(padx=20, pady=5, fill="x")
        self.proxy_entry.insert(0, self.master.proxy_url or "")

        self.test_button = ctk.CTkButton(self, text="测试连接", command=self.test_connection)
        self.test_button.pack(padx=20, pady=20)
        
        self.test_status_label = ctk.CTkLabel(self, text="")
        self.test_status_label.pack(padx=20, pady=5)

        self.save_button = ctk.CTkButton(self, text="保存并关闭", command=self.save_and_close, height=40)
        self.save_button.pack(padx=20, pady=20, side="bottom", fill="x")

    def save_and_close(self):
        new_api_key = self.api_key_entry.get().strip()
        new_proxy_url = self.proxy_entry.get().strip()
        
        config_data = { "GEMINI_API_KEY": new_api_key, "HTTP_PROXY": new_proxy_url }
        
        try:
            with open(CONFIG_FILE, "w") as f:
                json.dump(config_data, f, indent=4)
            self.master.api_key = new_api_key
            self.master.proxy_url = new_proxy_url
            self.destroy()
        except Exception as e:
            self.test_status_label.configure(text=f"❌ 保存失败: {e}", text_color="red")
    
    def test_connection(self):
        self.test_status_label.configure(text="测试中...", text_color="gray")
        self.test_button.configure(state="disabled")
        threading.Thread(target=self._test_worker, daemon=True).start()

    def _test_worker(self):
        proxy_url = self.proxy_entry.get().strip()
        proxies = { "http": proxy_url, "https": proxy_url } if proxy_url else None
        try:
            response = requests.get("https://www.google.com", proxies=proxies, timeout=10)
            if response.status_code == 200:
                self.after(0, self.update_test_status, "✅ 代理连接成功!", "green")
            else:
                self.after(0, self.update_test_status, f"❌ 代理连接失败 (状态码: {response.status_code})", "red")
        except Exception as e:
            self.after(0, self.update_test_status, f"❌ 代理连接失败: {type(e).__name__}", "red")

    def update_test_status(self, text, color):
        self.test_status_label.configure(text=text, text_color=color)
        self.test_button.configure(state="normal")

class AIStreamWindow(ctk.CTkToplevel):
    def __init__(self, master, title_text):
        super().__init__(master)
        self.title("🤖 AI 正在思考...")
        self.geometry("600x400")
        self.transient(master); self.grab_set(); self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.grid_rowconfigure(1, weight=1); self.grid_columnconfigure(0, weight=1)
        self.title_label = ctk.CTkLabel(self, text=f"正在处理: “{textwrap.shorten(title_text, width=50)}”", font=ctk.CTkFont(size=16, weight="bold")); self.title_label.grid(row=0, column=0, padx=20, pady=15)
        self.textbox = ctk.CTkTextbox(self, wrap="word", font=ctk.CTkFont(size=14)); self.textbox.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew"); self.textbox.insert("1.0", "AI正在连接并生成内容，请稍候...\n\n"); self.textbox.configure(state="disabled"); self.is_closed = False
    def update_content(self, chunk):
        if self.is_closed or not self.winfo_exists(): return
        self.textbox.configure(state="normal"); self.textbox.insert(ctk.END, chunk); self.textbox.see(ctk.END); self.textbox.configure(state="disabled"); self.update_idletasks()
    def on_close(self): self.is_closed = True; self.destroy()

class AIResponseWindow(ctk.CTkToplevel):
    def __init__(self, master, title_text, content_text):
        super().__init__(master)
        self.title("🤖 AI 创意分析报告"); self.geometry("700x600"); self.transient(master); self.grab_set(); self.grid_rowconfigure(1, weight=1); self.grid_columnconfigure(0, weight=1)
        self.title_label = ctk.CTkLabel(self, text=f"关于 “{textwrap.shorten(title_text, width=50)}”", font=ctk.CTkFont(size=18, weight="bold")); self.title_label.grid(row=0, column=0, padx=20, pady=15)
        self.textbox = ctk.CTkTextbox(self, wrap="word", font=ctk.CTkFont(size=14)); self.textbox.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew"); self.textbox.insert("1.0", content_text); self.textbox.configure(state="disabled")

class GraphViewWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master); self.master = master; self.title("想法图谱"); self.geometry("1000x800"); self.transient(master); self.protocol("WM_DELETE_WINDOW", self.on_close)
        self.fig, self.ax = plt.subplots(facecolor="#2B2B2B"); self.canvas = FigureCanvasTkAgg(self.fig, master=self); self.draw_graph(); self.canvas.get_tk_widget().pack(side=ctk.TOP, fill=ctk.BOTH, expand=True)
    def draw_graph(self):
        self.ax.clear(); G = nx.Graph(); active_ideas = [idea for idea in self.master.ideas if not idea.get('is_deleted', False)]
        for idea in active_ideas: G.add_node(idea['id'], label=idea['core_idea'][:10]+"...", status=idea.get('status', DEFAULT_STATUS))
        tags_to_ideas = {}; from itertools import combinations
        for idea in active_ideas:
            for tag in idea.get('keywords', []):
                if tag not in tags_to_ideas: tags_to_ideas[tag] = []
                tags_to_ideas[tag].append(idea['id'])
        for tag in tags_to_ideas:
            if len(tags_to_ideas[tag]) > 1:
                for u, v in combinations(tags_to_ideas[tag], 2):
                    if u != v: G.add_edge(u, v, type='tag')
        for idea in active_ideas:
            for link in idea.get('linked_ideas', []):
                if G.has_node(link['id']) and G.has_node(idea['id']):
                     if idea['id'] != link['id']: G.add_edge(idea['id'], link['id'], type='manual')
        if not G.nodes(): self.ax.text(0.5, 0.5, "没有足够的想法来构建图谱", color="white", ha="center", va="center"); self.canvas.draw(); return
        pos = nx.spring_layout(G, k=0.7, iterations=50); node_colors = [STATUS_COLORS.get(G.nodes[node]['status'], '#cccccc') for node in G.nodes]; labels = nx.get_node_attributes(G, 'label'); edge_colors = ['#fca311' if G.edges[e]['type'] == 'manual' else '#6c757d' for e in G.edges]; edge_widths = [2.0 if G.edges[e]['type'] == 'manual' else 0.7 for e in G.edges]
        nx.draw_networkx_nodes(G, pos, ax=self.ax, node_size=2200, node_color=node_colors, alpha=0.9, node_shape='o'); nx.draw_networkx_edges(G, pos, ax=self.ax, edge_color=edge_colors, width=edge_widths, alpha=0.7); nx.draw_networkx_labels(G, pos, ax=self.ax, labels=labels, font_size=9, font_color='#ffffff', font_weight='bold'); self.ax.set_facecolor("#2B2B2B"); self.fig.tight_layout(); self.canvas.draw()
    def on_close(self): plt.close(self.fig); self.master.graph_window = None; self.destroy()

class LinkerWindow(ctk.CTkToplevel):
    def __init__(self, master, source_idea_id):
        super().__init__(master); self.master = master; self.source_idea_id = source_idea_id; self.title("手动链接到..."); self.geometry("600x500"); self.transient(master); self.grab_set(); self.grid_columnconfigure(0, weight=1); self.grid_rowconfigure(0, weight=1); self.idea_list_frame = ctk.CTkScrollableFrame(self, label_text="选择要链接的想法"); self.idea_list_frame.grid(row=0, column=0, padx=15, pady=10, sticky="nsew"); self.confirm_button = ctk.CTkButton(self, text="确认链接", command=self.confirm_links); self.confirm_button.grid(row=1, column=0, padx=15, pady=10, sticky="ew"); self.link_checkboxes = {}; self.populate_idea_list()
    def populate_idea_list(self):
        source_idea = next((i for i in self.master.ideas if i['id'] == self.source_idea_id), None);
        if not source_idea: return
        existing_links = [link['id'] for link in source_idea.get('linked_ideas', [])]
        for idea in self.master.ideas:
            if idea['id'] == self.source_idea_id or idea.get('is_deleted', False) or idea['id'] in existing_links: continue
            var = ctk.StringVar(value=""); cb = ctk.CTkCheckBox(self.idea_list_frame, text=idea['core_idea'], variable=var, onvalue=idea['id'], offvalue=""); cb.pack(anchor="w", padx=10, pady=5); self.link_checkboxes[idea['id']] = var
    def confirm_links(self):
        selected_ids = [var.get() for var in self.link_checkboxes.values() if var.get()]
        if selected_ids: self.master.add_manual_links(self.source_idea_id, selected_ids)
        self.destroy()

class TagManagerWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master); self.master = master; self.title("标签管理器"); self.geometry("700x550"); self.transient(master); self.grab_set(); self.grid_columnconfigure(0, weight=1); self.grid_columnconfigure(1, weight=1); self.grid_rowconfigure(0, weight=1); self.left_frame = ctk.CTkFrame(self); self.left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew"); self.left_frame.grid_rowconfigure(0, weight=1); self.tag_list_frame = ctk.CTkScrollableFrame(self.left_frame, label_text="所有标签"); self.tag_list_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew"); self.right_frame = ctk.CTkFrame(self); self.right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew"); ctk.CTkLabel(self.right_frame, text="重命名选中标签:", font=ctk.CTkFont(weight="bold")).pack(padx=20, pady=(10, 5), anchor="w"); self.rename_entry = ctk.CTkEntry(self.right_frame, placeholder_text="输入新名称..."); self.rename_entry.pack(padx=20, pady=5, fill="x"); self.rename_button = ctk.CTkButton(self.right_frame, text="确认重命名", command=self.rename_tag); self.rename_button.pack(padx=20, pady=5, fill="x"); ctk.CTkLabel(self.right_frame, text="合并标签:", font=ctk.CTkFont(weight="bold")).pack(padx=20, pady=(20, 5), anchor="w"); self.merge_source_label = ctk.CTkLabel(self.right_frame, text="将: (在左侧选择)", wraplength=180); self.merge_source_label.pack(padx=20, pady=5, anchor="w"); ctk.CTkLabel(self.right_frame, text="合并到:").pack(padx=20, pady=5, anchor="w"); self.merge_target_combo = ctk.CTkComboBox(self.right_frame, values=["选择目标标签"]); self.merge_target_combo.pack(padx=20, pady=5, fill="x"); self.merge_button = ctk.CTkButton(self.right_frame, text="确认合并", command=self.merge_tags); self.merge_button.pack(padx=20, pady=5, fill="x"); ctk.CTkLabel(self.right_frame, text="删除选中标签:", font=ctk.CTkFont(weight="bold")).pack(padx=20, pady=(20, 5), anchor="w"); self.delete_info_label = ctk.CTkLabel(self.right_frame, text="(此操作仅从所有想法中移除该标签)", wraplength=180, text_color="gray"); self.delete_info_label.pack(padx=20, pady=2, anchor="w"); self.delete_button = ctk.CTkButton(self.right_frame, text="确认删除", fg_color="#D32F2F", hover_color="#B71C1C", command=self.delete_tag); self.delete_button.pack(padx=20, pady=5, fill="x"); self.selected_tags = []; self.all_tags = self.master.get_all_tags(); self.merge_target_combo.configure(values=self.all_tags); self.refresh_tag_list()
    def refresh_tag_list(self):
        for widget in self.tag_list_frame.winfo_children(): widget.destroy()
        self.all_tags = self.master.get_all_tags(); self.tag_buttons = {}
        for tag in self.all_tags:
            count = sum(1 for idea in self.master.ideas if tag in idea.get('keywords', [])); btn_text = f"{tag} ({count})"; btn = ctk.CTkButton(self.tag_list_frame, text=btn_text, fg_color="transparent", anchor="w", command=partial(self.select_tag, tag)); btn.pack(fill="x", padx=5, pady=2); self.tag_buttons[tag] = btn
    def select_tag(self, tag):
        if tag in self.selected_tags: self.selected_tags.remove(tag); self.tag_buttons[tag].configure(fg_color="transparent")
        else: self.selected_tags.append(tag); self.tag_buttons[tag].configure(fg_color="#3B8ED0")
        self.merge_source_label.configure(text=f"将: {', '.join(self.selected_tags)}")
    def rename_tag(self):
        if len(self.selected_tags) != 1: return
        old_name = self.selected_tags[0]; new_name = self.rename_entry.get().strip()
        if not new_name or new_name == old_name: return
        for idea in self.master.ideas:
            if 'keywords' in idea and old_name in idea['keywords']:
                idea['keywords'].remove(old_name)
                if new_name not in idea['keywords']:
                    idea['keywords'].append(new_name)
        self.post_action_update(); self.rename_entry.delete(0, 'end')
    def merge_tags(self):
        target_tag = self.merge_target_combo.get()
        if not self.selected_tags or not target_tag or "选择" in target_tag: return
        for idea in self.master.ideas:
            if 'keywords' in idea:
                idea_tags = set(idea['keywords'])
                if any(src_tag in idea_tags for src_tag in self.selected_tags):
                    idea_tags.add(target_tag); idea_tags.difference_update(self.selected_tags); idea['keywords'] = list(idea_tags)
        self.post_action_update()
    def delete_tag(self):
        if not self.selected_tags: return
        for idea in self.master.ideas:
            if 'keywords' in idea:
                idea['keywords'] = [k for k in idea['keywords'] if k not in self.selected_tags]
        self.post_action_update()
    def post_action_update(self):
        self.master.save_ideas(); self.master.update_tag_selection_area(); self.master.update_keyword_combos(); self.selected_tags = []; self.all_tags = self.master.get_all_tags(); self.merge_target_combo.configure(values=self.all_tags); self.refresh_tag_list(); self.master.update_graph()

class MarkdownPreviewWindow(ctk.CTkToplevel):
    def __init__(self, master, title_text, content_text):
        super().__init__(master)
        self.title("📝 Markdown 预览")
        self.geometry("800x600")
        self.transient(master)
        self.grab_set()

        self.grid_rowconfigure(1, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # 标题
        title_label = ctk.CTkLabel(
            self,
            text=f"预览: {title_text}",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=15)

        # 预览内容
        self.preview_textbox = ctk.CTkTextbox(
            self,
            wrap="word",
            font=ctk.CTkFont(size=14)
        )
        self.preview_textbox.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")

        # 简单的Markdown渲染
        rendered_content = self.simple_markdown_render(content_text)
        self.preview_textbox.insert("1.0", rendered_content)
        self.preview_textbox.configure(state="disabled")

    def simple_markdown_render(self, text):
        """简单的Markdown渲染"""
        lines = text.split('\n')
        rendered_lines = []

        for line in lines:
            # 标题处理
            if line.startswith('# '):
                rendered_lines.append(f"【标题1】{line[2:]}")
            elif line.startswith('## '):
                rendered_lines.append(f"【标题2】{line[3:]}")
            elif line.startswith('### '):
                rendered_lines.append(f"【标题3】{line[4:]}")
            # 粗体处理
            elif '**' in line:
                line = line.replace('**', '【粗体】')
                rendered_lines.append(line)
            # 斜体处理
            elif '*' in line:
                line = line.replace('*', '【斜体】')
                rendered_lines.append(line)
            # 列表处理
            elif line.startswith('- ') or line.startswith('* '):
                rendered_lines.append(f"  • {line[2:]}")
            elif line.startswith('1. ') or line.startswith('2. ') or line.startswith('3. '):
                rendered_lines.append(f"  {line}")
            else:
                rendered_lines.append(line)

        return '\n'.join(rendered_lines)

class ExportDialog(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master)
        self.master = master
        self.title("📤 导出想法")
        self.geometry("500x400")
        self.transient(master)
        self.grab_set()

        self.grid_rowconfigure(2, weight=1)
        self.grid_columnconfigure(0, weight=1)

        # 标题
        title_label = ctk.CTkLabel(self, text="选择导出格式和内容", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.grid(row=0, column=0, padx=20, pady=20)

        # 导出格式选择
        format_frame = ctk.CTkFrame(self)
        format_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")

        ctk.CTkLabel(format_frame, text="导出格式:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.format_var = ctk.StringVar(value="markdown")
        format_options = [
            ("Markdown (.md)", "markdown"),
            ("JSON (.json)", "json"),
            ("纯文本 (.txt)", "text"),
            ("CSV (.csv)", "csv")
        ]

        for text, value in format_options:
            radio = ctk.CTkRadioButton(format_frame, text=text, variable=self.format_var, value=value)
            radio.pack(anchor="w", padx=30, pady=2)

        # 内容选择
        content_frame = ctk.CTkFrame(self)
        content_frame.grid(row=2, column=0, padx=20, pady=10, sticky="nsew")

        ctk.CTkLabel(content_frame, text="导出内容:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.export_all_var = ctk.BooleanVar(value=True)
        self.export_active_var = ctk.BooleanVar(value=False)
        self.export_selected_var = ctk.BooleanVar(value=False)

        ctk.CTkCheckBox(content_frame, text="所有想法", variable=self.export_all_var).pack(anchor="w", padx=30, pady=2)
        ctk.CTkCheckBox(content_frame, text="仅活跃想法（未删除）", variable=self.export_active_var).pack(anchor="w", padx=30, pady=2)
        if self.master.current_idea_id:
            ctk.CTkCheckBox(content_frame, text="仅当前选中想法", variable=self.export_selected_var).pack(anchor="w", padx=30, pady=2)

        # 按钮
        button_frame = ctk.CTkFrame(self, fg_color="transparent")
        button_frame.grid(row=3, column=0, padx=20, pady=20, sticky="ew")
        button_frame.grid_columnconfigure(0, weight=1)

        export_button = ctk.CTkButton(button_frame, text="📤 开始导出", command=self.start_export, fg_color="#32CD32", hover_color="#228B22")
        export_button.grid(row=0, column=0, padx=(0, 10), sticky="e")

        cancel_button = ctk.CTkButton(button_frame, text="取消", command=self.destroy, fg_color="#DC143C", hover_color="#B22222")
        cancel_button.grid(row=0, column=1, sticky="e")

    def start_export(self):
        """开始导出过程"""
        format_type = self.format_var.get()

        # 确定要导出的想法
        ideas_to_export = []
        if self.export_selected_var.get() and self.master.current_idea_id:
            ideas_to_export = [idea for idea in self.master.ideas if idea['id'] == self.master.current_idea_id]
        elif self.export_active_var.get():
            ideas_to_export = [idea for idea in self.master.ideas if not idea.get('is_deleted', False)]
        else:  # export_all_var
            ideas_to_export = self.master.ideas

        if not ideas_to_export:
            self.master.show_status_message("❌ 没有可导出的想法", "red")
            return

        # 选择保存位置
        import tkinter.filedialog as fd
        file_extensions = {
            "markdown": [("Markdown files", "*.md")],
            "json": [("JSON files", "*.json")],
            "text": [("Text files", "*.txt")],
            "csv": [("CSV files", "*.csv")]
        }

        filename = fd.asksaveasfilename(
            title="保存导出文件",
            filetypes=file_extensions[format_type],
            defaultextension=f".{format_type if format_type != 'markdown' else 'md'}"
        )

        if filename:
            try:
                self.export_to_file(ideas_to_export, filename, format_type)
                self.master.show_status_message(f"✅ 已导出 {len(ideas_to_export)} 个想法", "green")
                self.destroy()
            except Exception as e:
                self.master.show_status_message(f"❌ 导出失败: {str(e)}", "red")

    def export_to_file(self, ideas, filename, format_type):
        """导出想法到文件"""
        if format_type == "markdown":
            self.export_markdown(ideas, filename)
        elif format_type == "json":
            self.export_json(ideas, filename)
        elif format_type == "text":
            self.export_text(ideas, filename)
        elif format_type == "csv":
            self.export_csv(ideas, filename)

    def export_markdown(self, ideas, filename):
        """导出为Markdown格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# 我的想法集合\n\n")
            f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"想法数量: {len(ideas)}\n\n")

            for idea in ideas:
                f.write(f"## {idea.get('core_idea', '无标题')}\n\n")
                f.write(f"**状态**: {idea.get('status', '未知')}\n\n")

                if idea.get('keywords'):
                    f.write(f"**标签**: {', '.join(idea['keywords'])}\n\n")

                if idea.get('context'):
                    f.write(f"### 背景/来源\n{idea['context']}\n\n")

                if idea.get('application'):
                    f.write(f"### 潜在应用\n{idea['application']}\n\n")

                if idea.get('questions'):
                    f.write(f"### 相关问题\n{idea['questions']}\n\n")

                if idea.get('linked_ideas'):
                    f.write("### 相关链接\n")
                    for link in idea['linked_ideas']:
                        linked_idea = next((i for i in self.master.ideas if i['id'] == link['id']), None)
                        if linked_idea:
                            f.write(f"- {linked_idea.get('core_idea', '未知想法')}\n")
                    f.write("\n")

                f.write("---\n\n")

    def export_json(self, ideas, filename):
        """导出为JSON格式"""
        export_data = {
            "export_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "total_ideas": len(ideas),
            "ideas": ideas
        }
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

    def export_text(self, ideas, filename):
        """导出为纯文本格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("我的想法集合\n")
            f.write("=" * 20 + "\n\n")
            f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"想法数量: {len(ideas)}\n\n")

            for i, idea in enumerate(ideas, 1):
                f.write(f"{i}. {idea.get('core_idea', '无标题')}\n")
                f.write("-" * 40 + "\n")
                f.write(f"状态: {idea.get('status', '未知')}\n")

                if idea.get('keywords'):
                    f.write(f"标签: {', '.join(idea['keywords'])}\n")

                if idea.get('context'):
                    f.write(f"背景: {idea['context']}\n")

                if idea.get('application'):
                    f.write(f"应用: {idea['application']}\n")

                if idea.get('questions'):
                    f.write(f"问题: {idea['questions']}\n")

                f.write("\n")

    def export_csv(self, ideas, filename):
        """导出为CSV格式"""
        import csv
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['核心想法', '状态', '标签', '背景', '应用', '问题', '创建时间'])

            for idea in ideas:
                writer.writerow([
                    idea.get('core_idea', ''),
                    idea.get('status', ''),
                    ', '.join(idea.get('keywords', [])),
                    idea.get('context', ''),
                    idea.get('application', ''),
                    idea.get('questions', ''),
                    time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(idea.get('id', 0) / 1000))
                ])

class RecycleBinWindow(ctk.CTkToplevel):
    def __init__(self, master):
        super().__init__(master); self.master = master; self.title("回收站"); self.geometry("600x500"); self.transient(master); self.grab_set(); self.grid_columnconfigure(0, weight=1); self.grid_rowconfigure(0, weight=1); self.selected_trash_id = None; self.trash_list_frame = ctk.CTkScrollableFrame(self, label_text="已删除的想法"); self.trash_list_frame.grid(row=0, column=0, columnspan=2, padx=15, pady=10, sticky="nsew"); self.button_frame = ctk.CTkFrame(self, fg_color="transparent"); self.button_frame.grid(row=1, column=0, columnspan=2, padx=15, pady=10, sticky="ew"); self.button_frame.grid_columnconfigure(0, weight=1); self.restore_button = ctk.CTkButton(self.button_frame, text="恢复选中", command=self.restore_selected); self.restore_button.grid(row=0, column=0, padx=(0, 5), sticky="ew"); self.perm_delete_button = ctk.CTkButton(self.button_frame, text="彻底删除", fg_color="#D32F2F", hover_color="#B71C1C", command=self.delete_selected_permanently); self.perm_delete_button.grid(row=0, column=1, padx=(5, 0), sticky="ew"); self.refresh_trash_list()
    def refresh_trash_list(self):
        for widget in self.trash_list_frame.winfo_children(): widget.destroy()
        deleted_ideas = [idea for idea in self.master.ideas if idea.get('is_deleted', False)]
        if not deleted_ideas: ctk.CTkLabel(self.trash_list_frame, text="回收站是空的").pack(expand=True); return
        for idea in deleted_ideas:
            idea_id = idea.get("id"); core_idea = idea.get("core_idea", "无标题想法"); btn = ctk.CTkButton(self.trash_list_frame, text=core_idea, fg_color="transparent", anchor="w", command=lambda id=idea_id: self.select_trash_item(id)); btn.pack(fill="x", padx=5, pady=2)
    def select_trash_item(self, idea_id): self.selected_trash_id = idea_id
    def restore_selected(self):
        if not self.selected_trash_id: return
        idea = next((item for item in self.master.ideas if item["id"] == self.selected_trash_id), None)
        if idea: idea['is_deleted'] = False; self.master.save_ideas(); self.master.refresh_idea_list(); self.refresh_trash_list(); self.selected_trash_id = None; self.master.update_graph()
    def delete_selected_permanently(self):
        if not self.selected_trash_id: return
        self.master.ideas = [idea for idea in self.master.ideas if idea["id"] != self.selected_trash_id]; self.master.save_ideas(); self.refresh_trash_list(); self.selected_trash_id = None; self.master.update_graph()

# ==============================================================================
# ---- MAIN APPLICATION CLASS ----
# ==============================================================================
class IdeaGeneratorApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("个人想法生成器 v4.3 - 终极修复版")
        self.geometry("1200x750")
        
        # Core App Variables
        self.ideas = []
        self.current_idea_id = None
        self.graph_window = None
        self.tag_checkboxes = {}
        self.api_key = None
        self.proxy_url = None
        self.status_label = None  # 用于显示保存状态
        self.auto_save_timer = None  # 自动保存定时器
        self.last_content_hash = None  # 用于检测内容变化

        # Load Config and Data
        self.load_config()
        self.load_ideas()

        # Setup UI
        self._setup_main_grid()
        self._create_left_frame()
        self._create_middle_frame()
        self._create_right_frame()
        
        # Initial Population
        self.refresh_idea_list()
        self.update_keyword_combos()
        self.update_tag_selection_area()

        # 设置快捷键
        self.setup_keyboard_shortcuts()

    def _setup_main_grid(self):
        self.grid_columnconfigure(0, weight=1, minsize=250)
        self.grid_columnconfigure(1, weight=3)
        self.grid_columnconfigure(2, weight=2, minsize=300)
        self.grid_rowconfigure(0, weight=1)

    def _create_left_frame(self):
        self.left_frame = ctk.CTkFrame(self, corner_radius=10)
        self.left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.left_frame.grid_rowconfigure(7, weight=1)

        # 改进按钮样式和图标
        self.new_idea_button = ctk.CTkButton(
            self.left_frame,
            text="💡 新建想法",
            font=ctk.CTkFont(size=16, weight="bold"),
            command=self.clear_form,
            fg_color="#2E8B57",
            hover_color="#228B22",
            height=40
        )
        self.new_idea_button.grid(row=0, column=0, padx=20, pady=(15, 5), sticky="ew")

        self.graph_view_button = ctk.CTkButton(
            self.left_frame,
            text="� 图谱视图",
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.open_graph_view,
            fg_color="#4169E1",
            hover_color="#1E90FF"
        )
        self.graph_view_button.grid(row=1, column=0, padx=20, pady=5, sticky="ew")

        # 搜索框样式改进
        self.search_entry = ctk.CTkEntry(
            self.left_frame,
            placeholder_text="🔍 搜索想法...",
            font=ctk.CTkFont(size=12)
        )
        self.search_entry.grid(row=2, column=0, padx=20, pady=5, sticky="ew")
        self.search_entry.bind("<KeyRelease>", self.filter_ideas)

        self.tag_manager_button = ctk.CTkButton(
            self.left_frame,
            text="🏷️ 标签管理",
            command=self.open_tag_manager,
            fg_color="#FF6347",
            hover_color="#FF4500"
        )
        self.tag_manager_button.grid(row=3, column=0, padx=20, pady=5, sticky="ew")

        self.trash_button = ctk.CTkButton(
            self.left_frame,
            text="🗑️ 回收站",
            command=self.open_recycle_bin_window,
            fg_color="#696969",
            hover_color="#808080"
        )
        self.trash_button.grid(row=4, column=0, padx=20, pady=5, sticky="ew")

        self.export_button = ctk.CTkButton(
            self.left_frame,
            text="📤 导出",
            command=self.open_export_dialog,
            fg_color="#8B4513",
            hover_color="#A0522D"
        )
        self.export_button.grid(row=5, column=0, padx=20, pady=5, sticky="ew")

        self.settings_button = ctk.CTkButton(
            self.left_frame,
            text="⚙️ 设置",
            command=self.open_settings_window,
            fg_color="#708090",
            hover_color="#778899"
        )
        self.settings_button.grid(row=6, column=0, padx=20, pady=5, sticky="ew")

        self.idea_list_frame = ctk.CTkScrollableFrame(self.left_frame, label_text="我的想法库")
        self.idea_list_frame.grid(row=7, column=0, padx=15, pady=(10, 15), sticky="nsew")

    def _create_middle_frame(self):
        self.middle_frame = ctk.CTkFrame(self, corner_radius=10)
        self.middle_frame.grid(row=0, column=1, padx=0, pady=10, sticky="nsew")
        self.middle_frame.grid_columnconfigure(0, weight=1)
        padding_y = 7

        # 创建分区框架来改善视觉层次
        # 基本信息区
        basic_info_frame = ctk.CTkFrame(self.middle_frame, fg_color="#2B2B2B", corner_radius=8)
        basic_info_frame.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="ew")
        basic_info_frame.grid_columnconfigure(1, weight=1)

        # 状态选择
        ctk.CTkLabel(basic_info_frame, text="状态:", anchor="w", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, padx=15, pady=(15, 5), sticky="w")
        self.status_combo = ctk.CTkComboBox(basic_info_frame, values=STATUS_LIST)
        self.status_combo.grid(row=0, column=1, padx=15, pady=(15, 5), sticky="ew")

        # 核心想法
        ctk.CTkLabel(basic_info_frame, text="核心想法:", anchor="w", font=ctk.CTkFont(weight="bold")).grid(row=1, column=0, padx=15, pady=(10, 15), sticky="w")

        core_idea_frame = ctk.CTkFrame(basic_info_frame, fg_color="transparent")
        core_idea_frame.grid(row=1, column=1, padx=15, pady=(10, 15), sticky="ew")
        core_idea_frame.grid_columnconfigure(0, weight=1)

        self.core_idea_entry = ctk.CTkEntry(core_idea_frame, font=ctk.CTkFont(size=14))
        self.core_idea_entry.grid(row=0, column=0, sticky="ew")
        self.core_idea_entry.bind("<KeyRelease>", lambda e: self.schedule_auto_save())
        
        self.ai_title_button = ctk.CTkButton(
            core_idea_frame,
            text="✨ AI标题",
            width=90,
            command=self.start_generate_title_ai,
            fg_color="#9370DB",
            hover_color="#8A2BE2",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.ai_title_button.grid(row=0, column=1, padx=(10, 0))

        ctk.CTkLabel(self.middle_frame, text="标签:", anchor="w").grid(row=2, column=0, padx=20, pady=(padding_y, 0), sticky="w")
        tag_area_frame = ctk.CTkFrame(self.middle_frame, fg_color="transparent")
        tag_area_frame.grid(row=2, column=1, columnspan=2, padx=20, pady=(padding_y, 0), sticky="ew")
        tag_area_frame.grid_columnconfigure(0, weight=1)

        self.tag_selection_frame = ctk.CTkScrollableFrame(tag_area_frame, label_text="选择或创建标签", orientation="horizontal", height=60)
        self.tag_selection_frame.grid(row=0, column=0, sticky="ew")

        tag_creation_frame = ctk.CTkFrame(tag_area_frame, fg_color="transparent")
        tag_creation_frame.grid(row=0, column=1, padx=(5, 0))

        self.add_tag_entry = ctk.CTkEntry(tag_creation_frame, placeholder_text="+新标签", width=80)
        self.add_tag_entry.pack()
        self.add_tag_entry.bind("<Return>", self.add_new_tag_to_selection)

        # AI建议风格选择
        self.ai_style_combo = ctk.CTkComboBox(
            tag_creation_frame,
            values=["通用风格", "技术风格", "商业风格", "创意风格", "学术风格"],
            width=80,
            font=ctk.CTkFont(size=10)
        )
        self.ai_style_combo.pack(pady=(2, 0))
        self.ai_style_combo.set("通用风格")

        ai_button_frame = ctk.CTkFrame(tag_creation_frame, fg_color="transparent")
        ai_button_frame.pack(pady=(2, 0))

        self.ai_suggest_button = ctk.CTkButton(
            ai_button_frame,
            text="🤖 AI建议",
            command=self.start_suggest_tags_ai,
            fg_color="#FF69B4",
            hover_color="#FF1493",
            font=ctk.CTkFont(size=10, weight="bold"),
            width=60
        )
        self.ai_suggest_button.pack(side="left", padx=(0, 2))

        self.ai_regenerate_button = ctk.CTkButton(
            ai_button_frame,
            text="🔄",
            command=self.regenerate_tags_ai,
            fg_color="#9370DB",
            hover_color="#8A2BE2",
            font=ctk.CTkFont(size=10, weight="bold"),
            width=20
        )
        self.ai_regenerate_button.pack(side="left")

        # 内容详情区
        content_frame = ctk.CTkFrame(self.middle_frame, fg_color="#1E1E1E", corner_radius=8)
        content_frame.grid(row=2, column=0, padx=15, pady=10, sticky="nsew")
        content_frame.grid_columnconfigure(0, weight=1)
        self.middle_frame.grid_rowconfigure(2, weight=1)

        ctk.CTkLabel(content_frame, text="📝 详细内容", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        ctk.CTkLabel(content_frame, text="背景/来源:", anchor="w", font=ctk.CTkFont(weight="bold")).grid(row=1, column=0, padx=15, pady=(10, 5), sticky="w")
        self.context_textbox = ctk.CTkTextbox(content_frame, height=60)
        self.context_textbox.grid(row=2, column=0, padx=15, pady=(0, 10), sticky="nsew")
        self.context_textbox.bind("<KeyRelease>", lambda e: self.schedule_auto_save())
        content_frame.grid_rowconfigure(2, weight=1)

        ctk.CTkLabel(content_frame, text="潜在应用:", anchor="w", font=ctk.CTkFont(weight="bold")).grid(row=3, column=0, padx=15, pady=(10, 5), sticky="w")
        self.application_textbox = ctk.CTkTextbox(content_frame, height=60)
        self.application_textbox.grid(row=4, column=0, padx=15, pady=(0, 10), sticky="nsew")
        self.application_textbox.bind("<KeyRelease>", lambda e: self.schedule_auto_save())
        content_frame.grid_rowconfigure(4, weight=1)

        ctk.CTkLabel(content_frame, text="相关问题:", anchor="w", font=ctk.CTkFont(weight="bold")).grid(row=5, column=0, padx=15, pady=(10, 5), sticky="w")
        self.questions_textbox = ctk.CTkTextbox(content_frame, height=60)
        self.questions_textbox.grid(row=6, column=0, padx=15, pady=(0, 15), sticky="nsew")
        self.questions_textbox.bind("<KeyRelease>", lambda e: self.schedule_auto_save())
        content_frame.grid_rowconfigure(6, weight=1)

        # 链接和操作区
        action_frame = ctk.CTkFrame(self.middle_frame, fg_color="#2B2B2B", corner_radius=8)
        action_frame.grid(row=3, column=0, padx=15, pady=10, sticky="ew")
        action_frame.grid_columnconfigure(0, weight=1)

        # 手动链接部分
        link_header_frame = ctk.CTkFrame(action_frame, fg_color="transparent")
        link_header_frame.grid(row=0, column=0, padx=15, pady=(15, 5), sticky="ew")
        link_header_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(link_header_frame, text="🔗 手动链接", font=ctk.CTkFont(size=14, weight="bold")).grid(row=0, column=0, sticky="w")
        self.add_link_button = ctk.CTkButton(
            link_header_frame,
            text="+ 添加链接",
            width=80,
            command=self.open_linker_window,
            fg_color="#4169E1",
            hover_color="#1E90FF",
            font=ctk.CTkFont(size=11)
        )
        self.add_link_button.grid(row=0, column=1, sticky="e")

        self.linked_items_container = ctk.CTkFrame(action_frame, fg_color="transparent")
        self.linked_items_container.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="nsew")

        # 操作按钮区
        bottom_button_frame = ctk.CTkFrame(self.middle_frame, fg_color="transparent")
        bottom_button_frame.grid(row=4, column=0, padx=15, pady=10, sticky="ew")
        bottom_button_frame.grid_columnconfigure(0, weight=1)

        self.ai_expand_button = ctk.CTkButton(
            bottom_button_frame,
            text="� AI 扩展想法",
            command=self.start_expand_idea_ai,
            fg_color="#20B2AA",
            hover_color="#008B8B",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.ai_expand_button.grid(row=0, column=0, sticky="w")

        self.preview_button = ctk.CTkButton(
            bottom_button_frame,
            text="📝 预览",
            command=self.open_markdown_preview,
            fg_color="#9932CC",
            hover_color="#8B008B",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.preview_button.grid(row=0, column=1, sticky="e", padx=5)

        self.save_button = ctk.CTkButton(
            bottom_button_frame,
            text="💾 保存想法",
            command=self.save_current_idea,
            fg_color="#32CD32",
            hover_color="#228B22",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.save_button.grid(row=0, column=2, sticky="e", padx=5)

        self.delete_button = ctk.CTkButton(
            bottom_button_frame,
            text="🗑️ 删除",
            fg_color="#DC143C",
            hover_color="#B22222",
            command=self.delete_current_idea,
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.delete_button.grid(row=0, column=3, sticky="e")

        # 添加状态栏
        self.status_label = ctk.CTkLabel(
            self.middle_frame,
            text="",
            font=ctk.CTkFont(size=11),
            text_color="gray"
        )
        self.status_label.grid(row=5, column=0, padx=15, pady=(5, 10), sticky="ew")

    def _create_right_frame(self):
        self.right_frame = ctk.CTkFrame(self, corner_radius=10)
        self.right_frame.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")
        self.right_frame.grid_rowconfigure(1, weight=1)
        self.right_frame.grid_columnconfigure((0, 1), weight=1)

        ctk.CTkLabel(self.right_frame, text="🤖 AI 创作助手", font=ctk.CTkFont(size=18, weight="bold")).grid(row=0, column=0, columnspan=2, padx=15, pady=15)
        
        self.generation_result_frame = ctk.CTkScrollableFrame(self.right_frame, label_text="生成结果")
        self.generation_result_frame.grid(row=1, column=0, columnspan=2, padx=15, pady=10, sticky="nsew")
        ctk.CTkLabel(self.generation_result_frame, text="这里将显示链接和生成结果。", justify="center").pack(expand=True)

        ctk.CTkLabel(self.right_frame, text="关键词碰撞:", font=ctk.CTkFont(size=14)).grid(row=2, column=0, columnspan=2, padx=15, pady=(15, 5))
        
        self.keyword_combo_1 = ctk.CTkComboBox(self.right_frame, values=[" "], command=self.perform_collision)
        self.keyword_combo_1.grid(row=3, column=0, padx=(15, 5), pady=5, sticky="ew")
        
        self.keyword_combo_2 = ctk.CTkComboBox(self.right_frame, values=[" "], command=self.perform_collision)
        self.keyword_combo_2.grid(row=3, column=1, padx=(5, 15), pady=5, sticky="ew")
        
        self.random_button = ctk.CTkButton(
            self.right_frame,
            text="⚡ 随机灵感碰撞 ⚡",
            height=45,
            font=ctk.CTkFont(size=16, weight="bold"),
            command=self.perform_random_inspiration,
            fg_color="#FF8C00",
            hover_color="#FF7F50"
        )
        self.random_button.grid(row=4, column=0, columnspan=2, padx=15, pady=20, sticky="ew")

    def open_settings_window(self):
        SettingsWindow(self)

    def open_export_dialog(self):
        """打开导出对话框"""
        ExportDialog(self)

    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        # Ctrl+N: 新建想法
        self.bind("<Control-n>", lambda e: self.clear_form())

        # Ctrl+S: 保存想法
        self.bind("<Control-s>", lambda e: self.save_current_idea())

        # Ctrl+E: 导出
        self.bind("<Control-e>", lambda e: self.open_export_dialog())

        # Ctrl+G: 图谱视图
        self.bind("<Control-g>", lambda e: self.open_graph_view())

        # Ctrl+T: 标签管理
        self.bind("<Control-t>", lambda e: self.open_tag_manager())

        # Ctrl+R: 回收站
        self.bind("<Control-r>", lambda e: self.open_recycle_bin_window())

        # Ctrl+P: 预览
        self.bind("<Control-p>", lambda e: self.open_markdown_preview())

        # F1: 显示快捷键帮助
        self.bind("<F1>", lambda e: self.show_shortcuts_help())

        # Delete: 删除当前想法
        self.bind("<Delete>", lambda e: self.delete_current_idea() if self.current_idea_id else None)

        # 设置焦点，使快捷键生效
        self.focus_set()

    def show_shortcuts_help(self):
        """显示快捷键帮助"""
        help_window = ctk.CTkToplevel(self)
        help_window.title("⌨️ 快捷键帮助")
        help_window.geometry("500x600")
        help_window.transient(self)
        help_window.grab_set()

        # 标题
        title_label = ctk.CTkLabel(help_window, text="键盘快捷键", font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=20)

        # 快捷键列表
        shortcuts_frame = ctk.CTkScrollableFrame(help_window)
        shortcuts_frame.pack(fill="both", expand=True, padx=20, pady=10)

        shortcuts = [
            ("Ctrl + N", "新建想法"),
            ("Ctrl + S", "保存当前想法"),
            ("Ctrl + E", "导出想法"),
            ("Ctrl + G", "打开图谱视图"),
            ("Ctrl + T", "标签管理器"),
            ("Ctrl + R", "打开回收站"),
            ("Ctrl + P", "预览当前想法"),
            ("Delete", "删除当前想法"),
            ("F1", "显示此帮助"),
        ]

        for key, description in shortcuts:
            shortcut_frame = ctk.CTkFrame(shortcuts_frame, fg_color="transparent")
            shortcut_frame.pack(fill="x", pady=5)

            key_label = ctk.CTkLabel(shortcut_frame, text=key, font=ctk.CTkFont(weight="bold", family="Consolas"))
            key_label.pack(side="left", padx=(10, 20))

            desc_label = ctk.CTkLabel(shortcut_frame, text=description)
            desc_label.pack(side="left")

        # 关闭按钮
        close_button = ctk.CTkButton(help_window, text="关闭", command=help_window.destroy)
        close_button.pack(pady=20)

    def show_status_message(self, message, color="gray", duration=3000):
        """显示状态消息"""
        if self.status_label:
            self.status_label.configure(text=message, text_color=color)
            # 设置定时器清除消息
            self.after(duration, lambda: self.status_label.configure(text="") if self.status_label else None)

    def get_current_content_hash(self):
        """获取当前内容的哈希值，用于检测变化"""
        import hashlib
        content = f"{self.core_idea_entry.get()}{self.context_textbox.get('1.0', 'end-1c')}{self.application_textbox.get('1.0', 'end-1c')}{self.questions_textbox.get('1.0', 'end-1c')}"
        return hashlib.md5(content.encode()).hexdigest()

    def schedule_auto_save(self):
        """安排自动保存"""
        if self.auto_save_timer:
            self.after_cancel(self.auto_save_timer)

        current_hash = self.get_current_content_hash()
        if current_hash != self.last_content_hash and self.core_idea_entry.get().strip():
            self.auto_save_timer = self.after(5000, self.auto_save)  # 5秒后自动保存

    def auto_save(self):
        """自动保存功能"""
        if self.core_idea_entry.get().strip():
            current_hash = self.get_current_content_hash()
            if current_hash != self.last_content_hash:
                self.save_current_idea()
                self.last_content_hash = current_hash
                self.show_status_message("💾 自动保存完成", "blue", 2000)

    def open_markdown_preview(self):
        """打开Markdown预览窗口"""
        if not self.core_idea_entry.get().strip():
            self.show_status_message("❌ 请先输入内容", "red")
            return

        # 收集所有内容
        title = self.core_idea_entry.get()
        content_parts = []

        # 添加状态
        status = self.status_combo.get()
        content_parts.append(f"**状态**: {status}\n")

        # 添加标签
        tags = [v.get() for v in self.tag_checkboxes.values() if v.get()]
        if tags:
            content_parts.append(f"**标签**: {', '.join(tags)}\n")

        # 添加各个部分
        context = self.context_textbox.get("1.0", "end-1c").strip()
        if context:
            content_parts.append(f"## 背景/来源\n{context}\n")

        application = self.application_textbox.get("1.0", "end-1c").strip()
        if application:
            content_parts.append(f"## 潜在应用\n{application}\n")

        questions = self.questions_textbox.get("1.0", "end-1c").strip()
        if questions:
            content_parts.append(f"## 相关问题\n{questions}\n")

        full_content = "\n".join(content_parts)
        MarkdownPreviewWindow(self, title, full_content)

    def load_config(self):
        if not os.path.exists(CONFIG_FILE):
            print(f"INFO: Config file not found at {CONFIG_FILE}. Creating a default one.")
            try:
                with open(CONFIG_FILE, "w") as f:
                    json.dump({"GEMINI_API_KEY": "YOUR_API_KEY_HERE", "HTTP_PROXY": ""}, f, indent=4)
                self.api_key = None
                self.proxy_url = None
            except Exception as e:
                print(f"ERROR: Could not create default config file: {e}")
            return

        try:
            with open(CONFIG_FILE, "r") as f:
                config = json.load(f)
                self.api_key = config.get("GEMINI_API_KEY")
                self.proxy_url = config.get("HTTP_PROXY")
                if not self.api_key or self.api_key == "YOUR_API_KEY_HERE":
                    print("ERROR: 'GEMINI_API_KEY' not found or is default in config.json.")
                    self.api_key = None
        except (json.JSONDecodeError, IOError) as e:
            print(f"ERROR: Could not decode JSON from {CONFIG_FILE}: {e}")
            self.api_key = None
            self.proxy_url = None

    def _configure_ai_model(self):
        if not self.api_key:
            print("ERROR: API Key not configured. Please check settings.")
            return None
        
        if self.proxy_url:
            os.environ['HTTP_PROXY'] = self.proxy_url
            os.environ['HTTPS_PROXY'] = self.proxy_url
        else:
            if 'HTTP_PROXY' in os.environ: del os.environ['HTTP_PROXY']
            if 'HTTPS_PROXY' in os.environ: del os.environ['HTTPS_PROXY']

        genai.configure(api_key=self.api_key)
        return genai.GenerativeModel('gemini-1.5-flash')

    def start_suggest_tags_ai(self):
        core_idea = self.core_idea_entry.get()
        if not self.api_key:
            self.show_status_message("❌ 请先配置API密钥", "red")
            return
        if not core_idea:
            self.show_status_message("❌ 请先输入核心想法", "red")
            return

        self.ai_suggest_button.configure(state="disabled", text="思考中...")
        threading.Thread(target=self.thread_suggest_tags_ai, args=(core_idea,), daemon=True).start()

    def regenerate_tags_ai(self):
        """重新生成标签建议"""
        self.start_suggest_tags_ai()

    def thread_suggest_tags_ai(self, core_idea):
        # 根据选择的风格生成不同的提示词
        style = self.ai_style_combo.get()
        style_prompts = {
            "通用风格": "分析以下想法，提供5个最相关的通用关键词。",
            "技术风格": "从技术角度分析以下想法，提供5个技术相关的关键词，如编程语言、框架、技术概念等。",
            "商业风格": "从商业角度分析以下想法，提供5个商业相关的关键词，如市场、盈利模式、用户群体等。",
            "创意风格": "从创意角度分析以下想法，提供5个富有创意和想象力的关键词。",
            "学术风格": "从学术研究角度分析以下想法，提供5个学术相关的关键词，如研究方法、理论框架等。"
        }

        style_prompt = style_prompts.get(style, style_prompts["通用风格"])
        prompt = f"{style_prompt}严格按逗号分隔格式返回，例如: AI,教育,市场营销\n核心想法: {core_idea}\n背景: {self.context_textbox.get('1.0', 'end-1c')}"
        stream_window = AIStreamWindow(self, f"为 “{core_idea}” 建议标签 ({style})")
        
        try:
            model = self._configure_ai_model()
            if not model: raise Exception("AI Model could not be configured.")
            response_stream = model.generate_content(prompt, stream=True)
            
            full_response = ""
            for chunk in response_stream:
                if chunk.text:
                    full_response += chunk.text
                    self.after(0, stream_window.update_content, chunk.text)
            
            self.after(0, self.finalize_suggest_tags, full_response, stream_window)
        except Exception as e:
            error_message = f"AI call failed: {e}"
            print(error_message)
            self.after(0, self.finalize_suggest_tags, f"Error: {error_message}", stream_window)

    def finalize_suggest_tags(self, response_text, stream_window):
        if stream_window.winfo_exists(): stream_window.destroy()
        print(f"DEBUG: AI suggested tags response: '{response_text}'")
        
        if response_text and not response_text.startswith("Error:"):
            tags = [t.strip() for t in response_text.strip().split(',') if t.strip()]
            if tags:
                for tag in tags: self.add_new_tag_to_selection(new_tag_name=tag, auto_select=True)
            else:
                print("INFO: AI returned empty or invalid tags.")
        
        self.ai_suggest_button.configure(state="normal", text="🤖 AI建议")

    def start_expand_idea_ai(self):
        if not self.api_key:
            print("ERROR: API Key not configured. Please open Settings."); return
        if not self.current_idea_id:
            print("ERROR: No idea selected"); return
        
        idea = next((i for i in self.ideas if i['id'] == self.current_idea_id), None)
        if not idea: return

        self.ai_expand_button.configure(state="disabled", text="分析中...")
        threading.Thread(target=self.thread_expand_idea_ai, args=(idea,), daemon=True).start()

    def thread_expand_idea_ai(self, idea):
        idea_content = f"核心想法: {idea.get('core_idea', '')}\n背景/来源: {idea.get('context', '')}\n潜在应用: {idea.get('application', '')}\n相关问题: {idea.get('questions', '')}"
        prompt = f"你是一位顶级的创意策略师。深入分析以下想法，并严格按照下面的格式和标题返回创意分析报告，不要添加任何额外解释。\n\n【扩展描述】\n(在此扩展核心想法为一段约100字的生动描述)\n\n【深度提问】\n(从用户价值、技术可行性、市场潜力和潜在风险四个角度，各提出一个深刻问题，以“-”开头)\n\n【应用构思】\n(构思一到两个具体的可执行应用场景并简要描述)\n\n--- 待分析的想法如下 ---\n{idea_content}"
        stream_window = AIStreamWindow(self, f"扩展 “{idea.get('core_idea', '')}”")

        try:
            model = self._configure_ai_model()
            if not model: raise Exception("AI Model could not be configured.")
            response_stream = model.generate_content(prompt, stream=True)

            full_response = ""
            for chunk in response_stream:
                if chunk.text:
                    full_response += chunk.text
                    self.after(0, stream_window.update_content, chunk.text)
            
            self.after(0, self.finalize_expand_idea, idea, full_response, stream_window)
        except Exception as e:
            error_message = f"AI call failed: {e}"
            print(error_message)
            self.after(0, self.finalize_expand_idea, idea, f"Error: {error_message}", stream_window)

    def finalize_expand_idea(self, idea, response_text, stream_window):
        if stream_window.winfo_exists(): stream_window.destroy()
        print(f"DEBUG: AI expansion response: '{response_text}'")

        if response_text and not response_text.startswith("Error:"):
            AIResponseWindow(self, title_text=idea.get('core_idea', ''), content_text=response_text)
        else:
            print("INFO: AI returned an empty or error response for expansion.")
        
        self.ai_expand_button.configure(state="normal", text="🤖 AI 扩展想法")
    
    def start_generate_title_ai(self):
        if not self.api_key:
            print("ERROR: API Key not configured. Please open Settings."); return
        
        content = self.get_full_content_for_summary()
        if not content:
            print("INFO: No content to summarize for a title."); return

        self.ai_title_button.configure(state="disabled", text="生成中...")
        threading.Thread(target=self.thread_generate_title_ai, args=(content,), daemon=True).start()

    def get_full_content_for_summary(self):
        context = self.context_textbox.get("1.0", "end-1c").strip()
        application = self.application_textbox.get("1.0", "end-1c").strip()
        questions = self.questions_textbox.get("1.0", "end-1c").strip()
        
        full_text = f"背景/来源:\n{context}\n\n潜在应用:\n{application}\n\n相关问题:\n{questions}"
        return full_text.strip()

    def thread_generate_title_ai(self, content):
        prompt = f"根据以下内容，为其生成一个简洁、精炼、不超过15个字的核心标题。直接返回标题文本，不要包含任何引号或多余的解释。\n\n--- 内容如下 ---\n{content}"
        stream_window = AIStreamWindow(self, "生成标题")
        
        try:
            model = self._configure_ai_model()
            if not model: raise Exception("AI Model could not be configured.")
            response_stream = model.generate_content(prompt, stream=True)

            full_response = ""
            for chunk in response_stream:
                if chunk.text:
                    full_response += chunk.text
                    self.after(0, stream_window.update_content, chunk.text)
            
            self.after(0, self.finalize_generate_title, full_response, stream_window)
        except Exception as e:
            error_message = f"AI call failed: {e}"
            print(error_message)
            self.after(0, self.finalize_generate_title, f"Error: {error_message}", stream_window)

    def finalize_generate_title(self, response_text, stream_window):
        if stream_window.winfo_exists(): stream_window.destroy()
        print(f"DEBUG: AI title generation response: '{response_text}'")

        if response_text and not response_text.startswith("Error:"):
            clean_title = response_text.strip().replace("*", "").replace("\"", "")
            self.core_idea_entry.delete(0, ctk.END)
            self.core_idea_entry.insert(0, clean_title)
        else:
            print("INFO: AI returned an empty or error response for title generation.")

        self.ai_title_button.configure(state="normal", text="✨ AI标题")

    def refresh_idea_list(self, ideas_to_display=None):
        for widget in self.idea_list_frame.winfo_children(): widget.destroy()
        active_ideas = ideas_to_display if ideas_to_display is not None else [i for i in self.ideas if not i.get('is_deleted')]
        for idea in sorted(active_ideas, key=lambda x: STATUS_LIST.index(x.get('status', DEFAULT_STATUS))):
            icon = idea.get('status', DEFAULT_STATUS).split(' ')[0]
            btn = ctk.CTkButton(self.idea_list_frame, text=f"{icon}  {idea.get('core_idea', 'N/A')}", fg_color="transparent", anchor="w", command=lambda i=idea.get("id"): self.display_idea(i))
            btn.pack(fill="x", padx=5, pady=2)
    
    def display_idea(self, idea_id):
        self.current_idea_id = idea_id; idea = next((item for item in self.ideas if item["id"] == idea_id), None)
        if not idea: return
        self.clear_form(clear_id=False); self.status_combo.set(idea.get("status", DEFAULT_STATUS)); self.core_idea_entry.delete(0, ctk.END); self.core_idea_entry.insert(0, idea.get("core_idea", "")); self.update_tag_selection_area(idea.get("keywords", [])); self.context_textbox.delete("1.0", ctk.END); self.context_textbox.insert("1.0", idea.get("context", "")); self.application_textbox.delete("1.0", ctk.END); self.application_textbox.insert("1.0", idea.get("application", "")); self.questions_textbox.delete("1.0", ctk.END); self.questions_textbox.insert("1.0", idea.get("questions", "")); self.refresh_manual_links_display(idea_id); self.perform_auto_link(idea_id)

    def save_current_idea(self):
        if not self.core_idea_entry.get():
            self.show_status_message("❌ 请输入核心想法后再保存", "red")
            return

        try:
            idea_data = {
                "id": self.current_idea_id or int(time.time() * 1000),
                "status": self.status_combo.get(),
                "core_idea": self.core_idea_entry.get(),
                "keywords": [v.get() for v in self.tag_checkboxes.values() if v.get()],
                "context": self.context_textbox.get("1.0", "end-1c"),
                "application": self.application_textbox.get("1.0", "end-1c"),
                "questions": self.questions_textbox.get("1.0", "end-1c"),
                "is_deleted": False,
                "linked_ideas": []
            }

            if self.current_idea_id:
                idea_idx = next((i for i, item in enumerate(self.ideas) if item["id"] == self.current_idea_id), None)
                if idea_idx is not None:
                    idea_data['linked_ideas'] = self.ideas[idea_idx].get('linked_ideas', [])
                    self.ideas[idea_idx] = idea_data
            else:
                self.ideas.append(idea_data)
                self.current_idea_id = idea_data["id"]

            self.save_ideas()
            self.refresh_idea_list()
            self.update_keyword_combos()
            self.update_tag_selection_area(idea_data["keywords"])
            self.update_graph()

            # 显示成功消息
            self.show_status_message("✅ 想法已保存", "green", 2000)

        except Exception as e:
            self.show_status_message(f"❌ 保存失败: {str(e)}", "red", 5000)

    def delete_current_idea(self):
        if not self.current_idea_id: return
        idea = next((item for item in self.ideas if item["id"] == self.current_idea_id), None)
        if idea: idea['is_deleted'] = True; self.save_ideas(); self.clear_form(); self.refresh_idea_list(); self.update_graph()

    def clear_form(self, clear_id=True):
        if clear_id: self.current_idea_id = None
        self.status_combo.set(DEFAULT_STATUS); self.core_idea_entry.delete(0, 'end'); self.update_tag_selection_area(); self.context_textbox.delete("1.0", "end"); self.application_textbox.delete("1.0", "end"); self.questions_textbox.delete("1.0", "end"); self.refresh_manual_links_display(None); self.core_idea_entry.focus()
    
    def open_linker_window(self):
        if self.current_idea_id: LinkerWindow(self, self.current_idea_id)

    def add_manual_links(self, source_id, target_ids):
        """添加手动链接，支持双向链接"""
        source_idea = next((i for i in self.ideas if i['id'] == source_id), None)
        if not source_idea:
            return

        if 'linked_ideas' not in source_idea:
            source_idea['linked_ideas'] = []

        for target_id in target_ids:
            if not target_id:
                continue

            target_id = int(target_id)
            target_idea = next((i for i in self.ideas if i['id'] == target_id), None)

            if not target_idea:
                continue

            # 添加从源到目标的链接
            if target_id not in [link['id'] for link in source_idea['linked_ideas']]:
                source_idea['linked_ideas'].append({'id': target_id, 'note': '', 'type': 'manual'})

            # 添加从目标到源的双向链接
            if 'linked_ideas' not in target_idea:
                target_idea['linked_ideas'] = []
            if source_id not in [link['id'] for link in target_idea['linked_ideas']]:
                target_idea['linked_ideas'].append({'id': source_id, 'note': '', 'type': 'manual'})

        self.save_ideas()
        self.refresh_manual_links_display(source_id)
        self.update_graph()
        self.show_status_message("✅ 双向链接已建立", "green", 2000)

    def remove_manual_link(self, source_id, target_id):
        """删除手动链接，支持双向删除"""
        source_idea = next((i for i in self.ideas if i['id'] == source_id), None)
        target_idea = next((i for i in self.ideas if i['id'] == target_id), None)

        # 从源想法中删除到目标的链接
        if source_idea and 'linked_ideas' in source_idea:
            source_idea['linked_ideas'] = [link for link in source_idea['linked_ideas'] if link['id'] != target_id]

        # 从目标想法中删除到源的链接
        if target_idea and 'linked_ideas' in target_idea:
            target_idea['linked_ideas'] = [link for link in target_idea['linked_ideas'] if link['id'] != source_id]

        self.save_ideas()
        self.refresh_manual_links_display(source_id)
        self.update_graph()
        self.show_status_message("✅ 双向链接已删除", "orange", 2000)

    def refresh_manual_links_display(self, idea_id):
        for widget in self.linked_items_container.winfo_children(): widget.destroy()
        if not idea_id: return
        idea = next((i for i in self.ideas if i['id'] == idea_id), None)
        if not (idea and idea.get('linked_ideas')): return
        for link_data in idea.get('linked_ideas', []):
            linked_idea = next((i for i in self.ideas if i['id'] == link_data['id']), None)
            if linked_idea:
                link_frame = ctk.CTkFrame(self.linked_items_container, fg_color="transparent"); link_frame.pack(fill="x")
                link_btn = ctk.CTkButton(link_frame, text=f"🔗 {linked_idea['core_idea']}", fg_color="transparent", anchor="w", command=lambda id=linked_idea['id']: self.display_idea(id)); link_btn.pack(side="left", expand=True, fill="x")
                remove_btn = ctk.CTkButton(link_frame, text="x", width=25, fg_color="transparent", text_color="gray", command=lambda s_id=idea_id, t_id=linked_idea['id']: self.remove_manual_link(s_id, t_id)); remove_btn.pack(side="right")

    def get_all_tags(self): return sorted(list(set(k for idea in self.ideas for k in idea.get("keywords", []))))
    def update_tag_selection_area(self, current_tags=None):
        for widget in self.tag_selection_frame.winfo_children(): widget.destroy()
        self.tag_checkboxes = {}
        for tag in self.get_all_tags():
            var = ctk.StringVar(value=tag if current_tags and tag in current_tags else ""); cb = ctk.CTkCheckBox(self.tag_selection_frame, text=tag, variable=var, onvalue=tag, offvalue=""); cb.pack(side="left", padx=5, pady=5); self.tag_checkboxes[tag] = var

    def add_new_tag_to_selection(self, event=None, new_tag_name=None, auto_select=False):
        new_tag = (new_tag_name or self.add_tag_entry.get()).strip()
        if not new_tag: return
        if new_tag in self.tag_checkboxes:
            if auto_select: self.tag_checkboxes[new_tag].set(new_tag)
        else:
            var = ctk.StringVar(value=new_tag if auto_select else ""); cb = ctk.CTkCheckBox(self.tag_selection_frame, text=new_tag, variable=var, onvalue=new_tag, offvalue=""); cb.pack(side="left", padx=5, pady=5); self.tag_checkboxes[new_tag] = var
        if not new_tag_name: self.add_tag_entry.delete(0, 'end')

    def open_tag_manager(self): TagManagerWindow(self)

    def open_graph_view(self):
        """打开图谱视图窗口"""
        if self.graph_window is None or not self.graph_window.winfo_exists():
            self.graph_window = GraphViewWindow(self)
        else:
            self.graph_window.lift()  # 将窗口提到前台
            self.graph_window.focus()

    def update_graph(self):
        """更新图谱视图（如果窗口已打开）"""
        if self.graph_window is not None and self.graph_window.winfo_exists():
            try:
                self.graph_window.draw_graph()
            except Exception as e:
                print(f"更新图谱时出错: {e}")

    def load_ideas(self):
        """加载想法数据 - 使用全局常量DB_FILE"""
        print(f"DEBUG: Using DB_FILE = {DB_FILE}")
        if os.path.exists(DB_FILE):
            try:
                with open(DB_FILE, 'r', encoding='utf-8') as f:
                    self.ideas = json.load(f)
                print(f"DEBUG: Loaded {len(self.ideas)} ideas")
            except (json.JSONDecodeError, IOError) as e:
                print(f"DEBUG: Error loading ideas: {e}")
                self.ideas = []
        else:
            print("DEBUG: DB_FILE does not exist, creating empty ideas list")
            self.ideas = []
            
            
    def save_ideas(self):
        """保存想法数据 - 使用全局常量DB_FILE"""
        with open(DB_FILE, 'w', encoding='utf-8') as f: 
            json.dump(self.ideas, f, ensure_ascii=False, indent=4)
    def open_recycle_bin_window(self): RecycleBinWindow(self)
    def filter_ideas(self, event=None):
        search_term = self.search_entry.get().lower()
        if search_term:
            self.refresh_idea_list([i for i in self.ideas if not i.get('is_deleted') and (search_term in i.get('core_idea','').lower() or any(search_term in t.lower() for t in i.get('keywords',[])))])
        else:
            self.refresh_idea_list()
    def update_keyword_combos(self):
        all_keywords = sorted(list(set(k for i in self.ideas if not i.get('is_deleted') for k in i.get("keywords", []))))
        if all_keywords:
            self.keyword_combo_1.configure(values=all_keywords); self.keyword_combo_2.configure(values=all_keywords)
            self.keyword_combo_1.set(all_keywords[0]); self.keyword_combo_2.set(all_keywords[1] if len(all_keywords) > 1 else "")
        else:
            self.keyword_combo_1.configure(values=[" "]); self.keyword_combo_2.configure(values=[" "]); self.keyword_combo_1.set(" "); self.keyword_combo_2.set(" ")
    def _clear_and_display_results(self, title, results):
        for widget in self.generation_result_frame.winfo_children(): widget.destroy()
        self.generation_result_frame.configure(label_text=title)
        if not results: ctk.CTkLabel(self.generation_result_frame, text="没有找到相关结果。", justify="center").pack(expand=True)
        else:
            for text, idea_id in results: btn = ctk.CTkButton(self.generation_result_frame, text=text, fg_color="transparent", anchor="w", command=lambda id=idea_id: self.display_idea(id)); btn.pack(fill="x", padx=5, pady=2)
    def perform_auto_link(self, idea_id):
        idea = next((i for i in self.ideas if i["id"] == idea_id), None)
        if not (idea and idea.get("keywords")): self._clear_and_display_results(f"与“{idea['core_idea'][:10]}...”的链接" if idea else "自动链接", []); return
        linked = [(i["core_idea"],i["id"]) for i in self.ideas if i["id"]!=idea_id and not i.get('is_deleted') and set(idea.get("keywords",[])).intersection(set(i.get("keywords",[])))]
        self._clear_and_display_results(f"与“{idea['core_idea'][:10]}...”的链接", linked)
    def perform_collision(self, event=None):
        k1, k2 = self.keyword_combo_1.get(), self.keyword_combo_2.get()
        if " " in k1 or " " in k2 or k1 == k2: return
        collided = [(f"[{','.join(i.get('keywords',[]))}] {i['core_idea']}", i['id']) for i in self.ideas if not i.get('is_deleted') and (k1 in i.get("keywords",[]) or k2 in i.get("keywords",[]))]
        self._clear_and_display_results(f"“{k1}” X “{k2}”", collided)
    def perform_random_inspiration(self):
        active = [i for i in self.ideas if not i.get('is_deleted')]
        if len(active) < 2: return
        i1, i2 = random.sample(active, 2)
        self._clear_and_display_results("随机灵感碰撞", [(f"【随机1】{i1['core_idea']}",i1['id']), (f"【随机2】{i2['core_idea']}",i2['id'])])

if __name__ == "__main__":
    ctk.set_appearance_mode("System")
    ctk.set_default_color_theme("blue")
    app = IdeaGeneratorApp()
    app.mainloop()




