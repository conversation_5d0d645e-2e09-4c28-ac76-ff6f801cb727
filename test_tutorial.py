#!/usr/bin/env python3
"""
新手教程测试脚本
用于测试和演示教程功能
"""

import customtkinter as ctk
import sys
import os

# 添加当前目录到路径，以便导入主应用
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from idea_generator import TutorialWindow, DesignSystem

class TutorialTestApp(ctk.CTk):
    """教程测试应用"""
    
    def __init__(self):
        super().__init__()
        self.title("🎓 新手教程测试")
        self.geometry("400x300")
        
        # 设置深色主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # 模拟主应用的属性
        self.tutorial_completed = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置测试界面"""
        # 主容器
        main_frame = ctk.CTkFrame(self, fg_color=DesignSystem.PRIMARY_BG)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="🎓 新手教程测试",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=DesignSystem.TEXT_PRIMARY
        )
        title_label.pack(pady=(30, 20))
        
        # 描述
        desc_label = ctk.CTkLabel(
            main_frame,
            text="点击下面的按钮来测试新手教程功能",
            font=ctk.CTkFont(size=14),
            text_color=DesignSystem.TEXT_SECONDARY
        )
        desc_label.pack(pady=(0, 30))
        
        # 测试按钮
        test_button = ctk.CTkButton(
            main_frame,
            text="🚀 启动新手教程",
            command=self.start_tutorial,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color=DesignSystem.ACCENT_GREEN,
            hover_color=DesignSystem.ACCENT_GREEN,
            width=200,
            height=50
        )
        test_button.pack(pady=10)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="准备就绪",
            font=ctk.CTkFont(size=12),
            text_color=DesignSystem.TEXT_SECONDARY
        )
        self.status_label.pack(pady=(20, 0))
        
        # 快捷键提示
        shortcut_label = ctk.CTkLabel(
            main_frame,
            text="快捷键: F2 - 打开教程",
            font=ctk.CTkFont(size=10),
            text_color=DesignSystem.TEXT_SECONDARY
        )
        shortcut_label.pack(side="bottom", pady=(0, 10))
        
        # 绑定快捷键
        self.bind("<F2>", lambda e: self.start_tutorial())
        self.focus_set()
    
    def start_tutorial(self):
        """启动教程"""
        self.status_label.configure(text="正在启动教程...")
        try:
            TutorialWindow(self)
            self.status_label.configure(text="教程已启动")
        except Exception as e:
            self.status_label.configure(text=f"启动失败: {e}")

def main():
    """主函数"""
    print("🎓 启动新手教程测试...")
    
    app = TutorialTestApp()
    
    print("✅ 教程测试应用已启动")
    print("💡 提示:")
    print("   - 点击按钮启动教程")
    print("   - 按 F2 快捷键也可以启动教程")
    print("   - 教程包含 6 个步骤，涵盖所有核心功能")
    
    app.mainloop()

if __name__ == "__main__":
    main()
